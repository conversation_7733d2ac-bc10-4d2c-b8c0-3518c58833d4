.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.calibration-steps {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  background-color: #e9ecef;
  color: #6c757d;
}

.step-number.active {
  background-color: #2B85E4;
  color: white;
}

.step-number.completed {
  background-color: #28a745;
  color: white;
}

.loading-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.step-line {
  width: 4rpx;
  height: 60rpx;
  background-color: #e9ecef;
  margin-top: 10rpx;
}

.step-line.completed {
  background-color: #28a745;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding-top: 10rpx;
}

.step-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.step-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.calibration-result {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.result-icon {
  font-size: 40rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #28a745;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  font-size: 28rpx;
  color: #333;
}

.result-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #2B85E4;
}

.action-buttons {
  padding: 20rpx;
}

.action-btn {
  width: 100%;
  padding: 30rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 15rpx;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.action-btn.primary {
  background-color: #2B85E4;
  color: white;
}

.action-btn.secondary {
  background-color: #28a745;
  color: white;
}

.action-btn.tertiary {
  background-color: #f8f9fa;
  color: #333;
  border: 1rpx solid #e9ecef;
}

.action-btn.disabled {
  background-color: #e9ecef;
  color: #6c757d;
}

.result-buttons {
  display: flex;
  gap: 15rpx;
}

.result-buttons .action-btn {
  flex: 1;
  margin-bottom: 0;
}
