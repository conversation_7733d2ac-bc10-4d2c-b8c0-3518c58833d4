<view class="container">
  <view class="settings-section">
    <view class="section-title">检测设置</view>

    <!-- 自动校准 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-name">自动校准</text>
        <text class="setting-desc">检测前自动进行设备校准</text>
      </view>
      <switch
        checked="{{deviceSettings.autoCalibration}}"
        bindchange="toggleAutoCalibration"
        color="#2B85E4"
      />
    </view>

    <!-- 检测模式 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-name">检测模式</text>
        <text class="setting-desc">选择检测精度和速度</text>
      </view>
      <picker
        range="{{detectionModes}}"
        range-key="name"
        value="{{detectionModeIndex}}"
        bindchange="onDetectionModeChange"
      >
        <view class="picker-value">
          {{detectionModeName}}
        </view>
      </picker>
    </view>

    <!-- 样品大小 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-name">样品大小</text>
        <text class="setting-desc">根据样品大小优化检测参数</text>
      </view>
      <picker
        range="{{sampleSizes}}"
        range-key="name"
        value="{{sampleSizeIndex}}"
        bindchange="onSampleSizeChange"
      >
        <view class="picker-value">
          {{sampleSizeName}}
        </view>
      </picker>
    </view>
  </view>

  <view class="settings-section">
    <view class="section-title">数据设置</view>

    <!-- 数据同步 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-name">数据同步</text>
        <text class="setting-desc">自动同步检测数据到云端</text>
      </view>
      <switch
        checked="{{deviceSettings.dataSync}}"
        bindchange="toggleDataSync"
        color="#2B85E4"
      />
    </view>
  </view>

  <view class="settings-section">
    <view class="section-title">电源设置</view>

    <!-- 省电模式 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-name">省电模式</text>
        <text class="setting-desc">降低功耗，延长电池使用时间</text>
      </view>
      <switch
        checked="{{deviceSettings.powerSaving}}"
        bindchange="togglePowerSaving"
        color="#2B85E4"
      />
    </view>
  </view>

  <!-- 重置按钮 -->
  <view class="reset-section">
    <button class="reset-btn" bindtap="resetSettings">恢复默认设置</button>
  </view>
</view>
