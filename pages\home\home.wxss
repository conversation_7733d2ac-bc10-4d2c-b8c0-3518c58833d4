.container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
}

.device-status .status-card {
  padding: 20rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connected {
  background-color: #e1f5e8;
  color: #2e7d32;
}

.disconnected {
  background-color: #ffebee;
  color: #c62828;
}

.data-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.data-card {
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  text-align: center;
}

.data-card .title {
  font-size: 28rpx;
  color: #666;
}

.data-card .value {
  font-size: 40rpx;
  font-weight: bold;
  margin-top: 10rpx;
}

.quick-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.primary-btn, .secondary-btn {
  flex: 1;
  height: 100rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.primary-btn {
  background: linear-gradient(135deg, #2B85E4 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(43, 133, 228, 0.3);
}

.secondary-btn {
  background-color: white;
  color: #2B85E4;
  border: 2rpx solid #2B85E4;
}

.primary-btn image, .secondary-btn image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 功能菜单 */
.function-menu {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.menu-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.menu-text {
  font-size: 24rpx;
  color: #666;
}
