.container {
  background: linear-gradient(180deg, #f8fafe 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 顶部横幅 */
.hero-banner {
  position: relative;
  height: 300rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.banner-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(43, 133, 228, 0.8) 0%, rgba(25, 118, 210, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-content {
  text-align: center;
  color: white;
}

.app-logo {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
  letter-spacing: 2rpx;
}

/* 用户信息卡片 */
.user-card {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(43, 133, 228, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 4rpx solid #e3f2fd;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.user-level {
  font-size: 24rpx;
  color: #2B85E4;
  background: #e3f2fd;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}

.user-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2B85E4;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 设备状态卡片 */
.device-status-card {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 15rpx;
}

.card-title {
  flex: 1;
}

.title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.subtitle {
  display: block;
  font-size: 22rpx;
  color: #666;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-indicator.connected .status-dot {
  background-color: #4caf50;
  box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.2);
}

.status-indicator.disconnected .status-dot {
  background-color: #f44336;
  box-shadow: 0 0 0 4rpx rgba(244, 67, 54, 0.2);
}

.status-text {
  font-size: 26rpx;
  font-weight: 500;
}

.status-indicator.connected .status-text {
  color: #4caf50;
}

.status-indicator.disconnected .status-text {
  color: #f44336;
}

.connect-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #2B85E4 0%, #1976d2 100%);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.connect-btn image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.device-info {
  text-align: right;
}

.device-name {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.device-id {
  font-size: 20rpx;
  color: #999;
}

/* 最新检测数据 */
.latest-detection {
  margin: 0 30rpx 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-left {
  display: flex;
  align-items: center;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.view-detail {
  font-size: 24rpx;
  color: #2B85E4;
}

.detection-result-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.sample-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.grade-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
  color: white;
}

.grade-badge.excellent {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.grade-badge.good {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.grade-badge.average {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.grade-badge.poor {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.components-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.component-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.component-item.primary {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 2rpx solid #2196f3;
}

.component-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.component-info {
  flex: 1;
}

.component-name {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.component-item.primary .component-name {
  color: #1976d2;
  font-weight: 500;
}

.component-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.component-item.primary .component-value {
  font-size: 32rpx;
  color: #1976d2;
}

.detection-time {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  font-size: 24rpx;
  color: #666;
}

.detection-time image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

/* 空状态 */
.no-data-state {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.no-data-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.no-data-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.no-data-hint {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 快速操作区域 */
.quick-actions-section {
  margin: 0 30rpx 30rpx;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.action-btn {
  border-radius: 20rpx;
  border: none;
  padding: 0;
  overflow: hidden;
}

.action-btn.primary {
  background: linear-gradient(135deg, #2B85E4 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 8rpx 30rpx rgba(43, 133, 228, 0.3);
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.btn-content {
  display: flex;
  align-items: center;
}

.btn-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.btn-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.btn-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.btn-subtitle {
  font-size: 22rpx;
  opacity: 0.8;
}

.btn-arrow {
  width: 30rpx;
  height: 30rpx;
}

.secondary-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn.secondary {
  flex: 1;
  height: 100rpx;
  background: white;
  color: #2B85E4;
  border: 2rpx solid #e3f2fd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(43, 133, 228, 0.1);
}

.action-btn.secondary .btn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 0;
  margin-bottom: 8rpx;
}

.action-btn.secondary .btn-title {
  font-size: 24rpx;
  font-weight: 500;
}

/* 专业服务网格 */
.services-grid {
  margin: 0 30rpx 30rpx;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.service-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.service-item:active {
  transform: scale(0.98);
}

.service-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15rpx;
}

.service-icon-wrapper.forum {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.service-icon-wrapper.analysis {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.service-icon-wrapper.report {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.service-icon-wrapper.settings {
  background: linear-gradient(135deg, #607d8b 0%, #455a64 100%);
}

.service-icon {
  width: 40rpx;
  height: 40rpx;
}

.service-name {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.service-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 行业资讯 */
.industry-news {
  margin: 0 30rpx 30rpx;
}

.view-more {
  font-size: 24rpx;
  color: #2B85E4;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.news-item {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.news-image {
  width: 120rpx;
  height: 90rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-summary {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-date {
  font-size: 20rpx;
  color: #999;
}

.news-source {
  font-size: 20rpx;
  color: #2B85E4;
  background: #e3f2fd;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
