.container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
}

.device-status .status-card {
  padding: 20rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connected {
  background-color: #e1f5e8;
  color: #2e7d32;
}

.disconnected {
  background-color: #ffebee;
  color: #c62828;
}

.data-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.data-card {
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  text-align: center;
}

.data-card .title {
  font-size: 28rpx;
  color: #666;
}

.data-card .value {
  font-size: 40rpx;
  font-weight: bold;
  margin-top: 10rpx;
}

.quick-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.quick-actions button {
  flex: 1;
  background-color: #2B85E4;
  color: white;
  border-radius: 8rpx;
}
