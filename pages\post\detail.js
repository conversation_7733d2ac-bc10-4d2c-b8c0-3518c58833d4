// pages/post/detail.js
const app = getApp()

Page({
  data: {
    post: null,
    comments: [],
    liked: false,
    showCommentModal: false,
    commentText: '',
    loading: true
  },

  onLoad(options) {
    const postId = options.id
    if (postId) {
      this.loadPostDetail(postId)
    } else {
      wx.showToast({
        title: '帖子不存在',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载帖子详情
  loadPostDetail(postId) {
    console.log('加载帖子详情:', postId)

    // 从本地存储获取帖子
    const localPosts = wx.getStorageSync('forum_posts') || []
    let post = localPosts.find(p => p.id === postId)

    // 如果本地没有，尝试示例数据
    if (!post) {
      const samplePosts = this.getSamplePosts()
      post = samplePosts.find(p => p._id === postId)
    }

    if (post) {
      // 格式化帖子数据
      const formattedPost = {
        ...post,
        categoryText: this.getCategoryText(post.category),
        createTime: this.formatDate(post.createTime),
        author: post.author || {
          nickname: '匿名用户',
          avatar: ''
        }
      }

      this.setData({
        post: formattedPost,
        loading: false
      })

      // 增加浏览量
      this.increaseViewCount(postId)

      // 加载评论
      this.loadComments(postId)

      // 检查是否已点赞
      this.checkLikeStatus(postId)

    } else {
      wx.showToast({
        title: '帖子不存在',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 获取示例帖子数据
  getSamplePosts() {
    return [
      {
        _id: 'sample_1',
        title: '铝土矿检测设备使用心得分享',
        content: '最近使用了新的检测设备，在精度和效率方面都有很大提升。特别是在铝含量检测方面，误差控制在±0.5%以内，比之前的设备提升了不少。\n\n主要优势：\n1. 检测精度高，误差小\n2. 操作简单，界面友好\n3. 检测速度快，效率高\n4. 数据可靠，稳定性好\n\n希望对大家有帮助！',
        category: 'experience',
        author: {
          nickname: '矿物专家',
          avatar: ''
        },
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        viewCount: 156,
        likeCount: 23,
        commentCount: 8,
        images: [],
        tags: ['设备使用', '经验分享', '检测精度']
      },
      {
        _id: 'sample_2',
        title: '关于硅含量检测异常的问题求助',
        content: '最近在检测过程中发现硅含量数据波动较大，不知道是设备问题还是样品处理问题，有遇到类似情况的朋友吗？\n\n具体情况：\n- 同一批样品，多次检测结果差异较大\n- 硅含量数据在15%-25%之间波动\n- 其他元素检测结果相对稳定\n- 设备校准正常\n\n求助各位专家指导！',
        category: 'question',
        author: {
          nickname: '新手小白',
          avatar: ''
        },
        createTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        viewCount: 89,
        likeCount: 12,
        commentCount: 15,
        images: [],
        tags: ['问题求助', '硅含量', '检测异常']
      }
    ]
  },

  // 获取分类文本
  getCategoryText(category) {
    const categoryMap = {
      'tech': '技术交流',
      'experience': '经验分享',
      'question': '问题求助',
      'news': '行业资讯',
      'equipment': '设备讨论',
      'other': '其他'
    }
    return categoryMap[category] || '其他'
  },

  // 格式化日期
  formatDate(timestamp) {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  // 增加浏览量
  increaseViewCount(postId) {
    // 在开发环境中模拟增加浏览量
    console.log('增加浏览量:', postId)
  },

  // 加载评论
  loadComments(postId) {
    // 模拟评论数据
    const mockComments = [
      {
        id: 'comment_1',
        content: '很有用的分享，感谢！',
        author: {
          nickname: '学习者',
          avatar: ''
        },
        createTime: '2小时前'
      },
      {
        id: 'comment_2',
        content: '我也遇到过类似问题，建议检查样品预处理流程。',
        author: {
          nickname: '经验丰富',
          avatar: ''
        },
        createTime: '1小时前'
      }
    ]

    this.setData({
      comments: postId === 'sample_1' || postId === 'sample_2' ? mockComments : []
    })
  },

  // 检查点赞状态
  checkLikeStatus(postId) {
    const likedPosts = wx.getStorageSync('liked_posts') || []
    this.setData({
      liked: likedPosts.includes(postId)
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 预览图片
  previewImage(e) {
    const urls = e.currentTarget.dataset.urls
    const current = e.currentTarget.dataset.current

    wx.previewImage({
      urls,
      current
    })
  },

  // 切换点赞
  toggleLike() {
    const postId = this.data.post.id || this.data.post._id
    const liked = !this.data.liked

    this.setData({ liked })

    // 更新本地点赞状态
    let likedPosts = wx.getStorageSync('liked_posts') || []
    if (liked) {
      if (!likedPosts.includes(postId)) {
        likedPosts.push(postId)
      }
    } else {
      likedPosts = likedPosts.filter(id => id !== postId)
    }
    wx.setStorageSync('liked_posts', likedPosts)

    // 更新点赞数
    const newLikeCount = (this.data.post.likeCount || 0) + (liked ? 1 : -1)
    this.setData({
      'post.likeCount': Math.max(0, newLikeCount)
    })

    wx.showToast({
      title: liked ? '点赞成功' : '取消点赞',
      icon: 'success'
    })
  },

  // 显示评论输入框
  showCommentInput() {
    this.setData({
      showCommentModal: true
    })
  },

  // 隐藏评论输入框
  hideCommentInput() {
    this.setData({
      showCommentModal: false,
      commentText: ''
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击事件冒泡
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    })
  },

  // 提交评论
  submitComment() {
    const commentText = this.data.commentText.trim()
    if (!commentText) {
      return
    }

    const userInfo = app.globalData.userInfo
    const isGuest = app.globalData.isGuest

    const newComment = {
      id: 'comment_' + Date.now(),
      content: commentText,
      author: {
        nickname: isGuest ? '游客用户' : (userInfo?.nickName || '用户'),
        avatar: isGuest ? '' : (userInfo?.avatarUrl || '')
      },
      createTime: '刚刚'
    }

    // 添加到评论列表
    const comments = [...this.data.comments, newComment]
    this.setData({
      comments,
      showCommentModal: false,
      commentText: '',
      'post.commentCount': comments.length
    })

    wx.showToast({
      title: '评论成功',
      icon: 'success'
    })
  },

  // 分享帖子
  sharePost() {
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        const actions = ['分享给朋友', '分享到朋友圈', '复制链接']
        wx.showToast({
          title: actions[res.tapIndex],
          icon: 'success'
        })
      }
    })
  },

  // 页面分享
  onShareAppMessage() {
    const post = this.data.post
    return {
      title: post ? post.title : '帖子详情',
      path: `/pages/post/detail?id=${post ? (post.id || post._id) : ''}`,
      imageUrl: post && post.images && post.images.length > 0 ? post.images[0] : ''
    }
  }
})