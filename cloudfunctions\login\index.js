const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { userInfo } = event

  try {
    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      _openid: wxContext.OPENID
    }).get()

    let userData = {
      _openid: wxContext.OPENID,
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      lastLoginTime: new Date(),
      loginCount: 1
    }

    if (userResult.data.length > 0) {
      // 用户已存在，更新登录信息
      await db.collection('users').doc(userResult.data[0]._id).update({
        data: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          lastLoginTime: new Date(),
          loginCount: db.command.inc(1)
        }
      })
      userData = { ...userResult.data[0], ...userData }
    } else {
      // 新用户，创建用户记录
      userData.createTime = new Date()
      await db.collection('users').add({
        data: userData
      })
    }

    return {
      success: true,
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID,
      userData
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
