// pages/settings/settings.js
const app = getApp()

Page({
  data: {
    // 应用设置
    currentLanguage: '简体中文',
    currentTheme: '默认主题',
    currentFontSize: '标准',
    autoSave: true,

    // 检测设置
    detectionAccuracy: '高精度',
    detectionUnit: '百分比(%)',
    autoCalibration: true,
    detectionAlert: true,

    // 数据设置
    cloudSync: false,
    usedStorage: 2.5,
    totalStorage: 10,

    // 通知设置
    pushNotification: true,
    forumNotification: true,
    updateNotification: true,

    // 高级设置
    developerMode: false,
    performanceMonitor: false,
    errorReporting: true,

    // 应用信息
    appVersion: '1.0.0'
  },

  onLoad() {
    this.loadSettings()
    this.calculateStorageUsage()
  },

  // 加载设置
  loadSettings() {
    const settings = wx.getStorageSync('app_settings') || {}

    this.setData({
      currentLanguage: settings.language || '简体中文',
      currentTheme: settings.theme || '默认主题',
      currentFontSize: settings.fontSize || '标准',
      autoSave: settings.autoSave !== false,
      detectionAccuracy: settings.detectionAccuracy || '高精度',
      detectionUnit: settings.detectionUnit || '百分比(%)',
      autoCalibration: settings.autoCalibration !== false,
      detectionAlert: settings.detectionAlert !== false,
      cloudSync: settings.cloudSync || false,
      pushNotification: settings.pushNotification !== false,
      forumNotification: settings.forumNotification !== false,
      updateNotification: settings.updateNotification !== false,
      developerMode: settings.developerMode || false,
      performanceMonitor: settings.performanceMonitor || false,
      errorReporting: settings.errorReporting !== false
    })
  },

  // 保存设置
  saveSettings() {
    const settings = {
      language: this.data.currentLanguage,
      theme: this.data.currentTheme,
      fontSize: this.data.currentFontSize,
      autoSave: this.data.autoSave,
      detectionAccuracy: this.data.detectionAccuracy,
      detectionUnit: this.data.detectionUnit,
      autoCalibration: this.data.autoCalibration,
      detectionAlert: this.data.detectionAlert,
      cloudSync: this.data.cloudSync,
      pushNotification: this.data.pushNotification,
      forumNotification: this.data.forumNotification,
      updateNotification: this.data.updateNotification,
      developerMode: this.data.developerMode,
      performanceMonitor: this.data.performanceMonitor,
      errorReporting: this.data.errorReporting
    }

    wx.setStorageSync('app_settings', settings)
    console.log('设置已保存:', settings)
  },

  // 计算存储使用量
  calculateStorageUsage() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const usedKB = storageInfo.currentSize
      const limitKB = storageInfo.limitSize

      this.setData({
        usedStorage: (usedKB / 1024).toFixed(1),
        totalStorage: (limitKB / 1024).toFixed(0)
      })
    } catch (error) {
      console.error('获取存储信息失败:', error)
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 设置语言
  setLanguage() {
    wx.showActionSheet({
      itemList: ['简体中文', 'English', '繁體中文'],
      success: (res) => {
        const languages = ['简体中文', 'English', '繁體中文']
        this.setData({ currentLanguage: languages[res.tapIndex] })
        this.saveSettings()

        wx.showToast({
          title: `已切换到${languages[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  // 设置主题
  setTheme() {
    wx.showActionSheet({
      itemList: ['默认主题', '深色主题', '护眼主题', '高对比度'],
      success: (res) => {
        const themes = ['默认主题', '深色主题', '护眼主题', '高对比度']
        this.setData({ currentTheme: themes[res.tapIndex] })
        this.saveSettings()

        wx.showToast({
          title: `已切换到${themes[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  // 设置字体大小
  setFontSize() {
    wx.showActionSheet({
      itemList: ['小号', '标准', '大号', '超大号'],
      success: (res) => {
        const sizes = ['小号', '标准', '大号', '超大号']
        this.setData({ currentFontSize: sizes[res.tapIndex] })
        this.saveSettings()

        wx.showToast({
          title: `字体大小已设置为${sizes[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  // 自动保存开关
  onAutoSaveChange(e) {
    this.setData({ autoSave: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启自动保存' : '已关闭自动保存',
      icon: 'success'
    })
  },

  // 设置检测精度
  setDetectionAccuracy() {
    wx.showActionSheet({
      itemList: ['标准精度', '高精度', '超高精度'],
      success: (res) => {
        const accuracies = ['标准精度', '高精度', '超高精度']
        this.setData({ detectionAccuracy: accuracies[res.tapIndex] })
        this.saveSettings()

        wx.showToast({
          title: `检测精度已设置为${accuracies[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  // 设置检测单位
  setDetectionUnit() {
    wx.showActionSheet({
      itemList: ['百分比(%)', 'ppm', 'mg/kg', 'g/t'],
      success: (res) => {
        const units = ['百分比(%)', 'ppm', 'mg/kg', 'g/t']
        this.setData({ detectionUnit: units[res.tapIndex] })
        this.saveSettings()

        wx.showToast({
          title: `检测单位已设置为${units[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  // 自动校准开关
  onAutoCalibrationChange(e) {
    this.setData({ autoCalibration: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启自动校准' : '已关闭自动校准',
      icon: 'success'
    })
  },

  // 检测提醒开关
  onDetectionAlertChange(e) {
    this.setData({ detectionAlert: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启检测提醒' : '已关闭检测提醒',
      icon: 'success'
    })
  },

  // 云端同步开关
  onCloudSyncChange(e) {
    this.setData({ cloudSync: e.detail.value })
    this.saveSettings()

    if (e.detail.value) {
      wx.showModal({
        title: '云端同步',
        content: '开启云端同步后，您的检测数据将自动备份到云端，确保数据安全。',
        showCancel: false
      })
    }

    wx.showToast({
      title: e.detail.value ? '已开启云端同步' : '已关闭云端同步',
      icon: 'success'
    })
  },

  // 数据备份
  backupData() {
    wx.showLoading({ title: '备份中...' })

    // 模拟备份过程
    setTimeout(() => {
      wx.hideLoading()
      wx.showModal({
        title: '备份完成',
        content: '数据已成功备份到本地。备份文件包含您的检测记录、设置信息等。',
        showCancel: false
      })
    }, 2000)
  },

  // 数据清理
  clearData() {
    wx.showModal({
      title: '清理数据',
      content: '确定要清理缓存和临时数据吗？这不会删除您的检测记录。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '清理中...' })

          // 模拟清理过程
          setTimeout(() => {
            wx.hideLoading()
            this.calculateStorageUsage()
            wx.showToast({
              title: '清理完成',
              icon: 'success'
            })
          }, 1500)
        }
      }
    })
  },

  // 查看存储空间
  viewStorage() {
    const { usedStorage, totalStorage } = this.data
    const usagePercent = ((usedStorage / totalStorage) * 100).toFixed(1)

    wx.showModal({
      title: '存储空间详情',
      content: `已使用：${usedStorage}MB\n总容量：${totalStorage}MB\n使用率：${usagePercent}%\n\n建议定期清理缓存以释放空间。`,
      showCancel: false
    })
  },

  // 推送通知开关
  onPushNotificationChange(e) {
    this.setData({ pushNotification: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启推送通知' : '已关闭推送通知',
      icon: 'success'
    })
  },

  // 论坛通知开关
  onForumNotificationChange(e) {
    this.setData({ forumNotification: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启论坛通知' : '已关闭论坛通知',
      icon: 'success'
    })
  },

  // 更新通知开关
  onUpdateNotificationChange(e) {
    this.setData({ updateNotification: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启更新通知' : '已关闭更新通知',
      icon: 'success'
    })
  },

  // 开发者模式开关
  onDeveloperModeChange(e) {
    this.setData({ developerMode: e.detail.value })
    this.saveSettings()

    if (e.detail.value) {
      wx.showModal({
        title: '开发者模式',
        content: '开发者模式已开启。您将看到调试信息和详细日志，这可能会影响应用性能。',
        showCancel: false
      })
    }

    wx.showToast({
      title: e.detail.value ? '已开启开发者模式' : '已关闭开发者模式',
      icon: 'success'
    })
  },

  // 性能监控开关
  onPerformanceMonitorChange(e) {
    this.setData({ performanceMonitor: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启性能监控' : '已关闭性能监控',
      icon: 'success'
    })
  },

  // 错误报告开关
  onErrorReportingChange(e) {
    this.setData({ errorReporting: e.detail.value })
    this.saveSettings()

    wx.showToast({
      title: e.detail.value ? '已开启错误报告' : '已关闭错误报告',
      icon: 'success'
    })
  },

  // 重置设置
  resetSettings() {
    wx.showModal({
      title: '重置设置',
      content: '确定要将所有设置恢复到默认值吗？此操作不可撤销。',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          // 清除本地设置
          wx.removeStorageSync('app_settings')

          // 重新加载默认设置
          this.setData({
            currentLanguage: '简体中文',
            currentTheme: '默认主题',
            currentFontSize: '标准',
            autoSave: true,
            detectionAccuracy: '高精度',
            detectionUnit: '百分比(%)',
            autoCalibration: true,
            detectionAlert: true,
            cloudSync: false,
            pushNotification: true,
            forumNotification: true,
            updateNotification: true,
            developerMode: false,
            performanceMonitor: false,
            errorReporting: true
          })

          wx.showToast({
            title: '设置已重置',
            icon: 'success'
          })
        }
      }
    })
  },

  // 查看版本信息
  viewVersionInfo() {
    wx.showModal({
      title: '版本信息',
      content: `智能铝土矿检测系统\n版本：${this.data.appVersion}\n构建时间：2024-01-01\n\n更新内容：\n• 新增论坛功能\n• 优化检测算法\n• 修复已知问题\n• 提升用户体验`,
      showCancel: false
    })
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({ title: '检查中...' })

    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading()
      wx.showModal({
        title: '检查更新',
        content: '当前已是最新版本！\n\n如有新版本发布，我们会第一时间通知您。',
        showCancel: false
      })
    }, 2000)
  },

  // 查看用户协议
  viewUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '感谢您使用智能铝土矿检测系统！\n\n请遵守以下使用规范：\n1. 合理使用检测功能\n2. 不发布违法违规内容\n3. 保护个人隐私信息\n4. 尊重知识产权\n\n详细协议内容请访问官网查看。',
      showCancel: false
    })
  },

  // 联系我们
  contactUs() {
    wx.showActionSheet({
      itemList: ['在线客服', '电话咨询', '邮件反馈', '意见建议'],
      success: (res) => {
        const actions = ['在线客服', '电话咨询', '邮件反馈', '意见建议']
        const contacts = [
          '客服微信：service_ai_detection',
          '客服电话：400-123-4567',
          '邮箱：<EMAIL>',
          '建议反馈功能开发中...'
        ]

        wx.showModal({
          title: actions[res.tapIndex],
          content: contacts[res.tapIndex],
          showCancel: false
        })
      }
    })
  }
})