.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 当前连接设备 */
.current-device {
  margin-bottom: 30rpx;
}

.device-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.device-card.connected {
  border: 2rpx solid #4caf50;
  background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.device-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.device-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-details {
  flex: 1;
}

.device-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.device-id {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.connection-status {
  display: block;
  font-size: 26rpx;
  color: #4caf50;
  font-weight: 500;
}

.device-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  background-color: #2B85E4;
  color: white;
  font-size: 28rpx;
  border: none;
}

.action-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
}

/* 扫描区域 */
.scan-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.scan-btn {
  display: flex;
  align-items: center;
  padding: 15rpx 25rpx;
  background-color: #2B85E4;
  color: white;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
}

.scan-btn.scanning {
  background-color: #ff9800;
}

.scan-icon {
  font-size: 30rpx;
  margin-right: 10rpx;
}

.scan-btn.scanning .scan-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 设备列表 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background-color: white;
  border-radius: 16rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
  display: block;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

.device-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.device-item.active {
  background-color: #e3f2fd;
  border: 2rpx solid #2196f3;
}

.signal-strength {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.signal-label {
  font-size: 22rpx;
  color: #999;
  margin-right: 10rpx;
}

.signal-bars {
  display: flex;
  gap: 3rpx;
  margin-right: 10rpx;
}

.signal-bar {
  width: 6rpx;
  height: 20rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
}

.signal-bar.active {
  background-color: #4caf50;
}

.signal-bar:nth-child(2) {
  height: 16rpx;
}

.signal-bar:nth-child(3) {
  height: 12rpx;
}

.signal-bar:nth-child(4) {
  height: 8rpx;
}

.signal-value {
  font-size: 20rpx;
  color: #999;
}

.connect-status {
  display: flex;
  align-items: center;
}

.connected-text {
  font-size: 24rpx;
  color: #4caf50;
  font-weight: 500;
}

.connect-btn {
  padding: 10rpx 20rpx;
  background-color: #2B85E4;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

/* 设备管理 */
.device-management {
  margin-bottom: 30rpx;
}

.management-options {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.option-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-child {
  border-bottom: none;
}

.option-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.option-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 连接提示 */
.connection-tips {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
