# 智能铝土矿检测小程序

一款专业的铝土矿成分检测微信小程序，集成了智能设备连接、精准成分分析、数据管理和专业交流等功能。

## 功能特色

### 🔬 智能检测
- **设备连接**: 支持蓝牙连接智能检测设备
- **成分分析**: 精准检测铝、硅、铁、钛等元素含量
- **实时监控**: 检测过程可视化，实时显示检测进度
- **品质评级**: 根据检测结果自动评定品质等级

### 📊 数据管理
- **历史记录**: 完整的检测历史记录管理
- **数据筛选**: 支持按时间、品质等级筛选数据
- **数据导出**: 支持Excel、PDF格式数据导出
- **云端同步**: 数据云端存储，多设备同步

### 💬 交流论坛
- **技术交流**: 专业的铝土矿检测技术讨论
- **经验分享**: 用户检测经验和心得分享
- **问题求助**: 遇到问题可以寻求专业帮助
- **行业动态**: 最新的行业资讯和技术动态

### ⚙️ 设备管理
- **设备扫描**: 自动扫描附近的检测设备
- **连接管理**: 设备连接状态监控和管理
- **设备校准**: 支持设备校准功能
- **设备信息**: 查看设备详细信息和状态

## 技术架构

### 前端技术
- **框架**: 微信小程序原生开发
- **UI设计**: 现代化Material Design风格
- **数据可视化**: 自定义Canvas图表组件
- **状态管理**: 全局数据状态管理

### 后端技术
- **云开发**: 微信云开发平台
- **数据库**: 云数据库存储用户和检测数据
- **云函数**: 处理用户登录、数据导出等业务逻辑
- **云存储**: 存储用户头像、检测报告等文件

### 设备连接
- **蓝牙通信**: 使用微信小程序蓝牙API
- **数据协议**: 自定义设备通信协议
- **实时传输**: 支持实时数据传输和处理

## 页面结构

```
pages/
├── home/           # 首页 - 快速检测和功能导航
├── device/         # 设备管理 - 设备连接和管理
├── forum/          # 交流论坛 - 技术交流和讨论
├── profile/        # 个人中心 - 用户信息和设置
├── login/          # 登录页面 - 用户授权登录
├── detection/      # 检测页面 - 智能检测功能
├── history/        # 历史记录 - 检测数据管理
├── settings/       # 系统设置 - 应用配置
├── post/           # 帖子相关 - 发布和查看帖子
└── about/          # 关于我们 - 应用信息
```

## 核心功能实现

### 1. 设备连接流程
```javascript
// 扫描设备
wx.startBluetoothDevicesDiscovery()

// 连接设备
wx.connectBLEDevice({
  deviceId: deviceId,
  success: () => {
    // 连接成功处理
  }
})

// 数据通信
wx.writeBLECharacteristicValue({
  deviceId: deviceId,
  serviceId: serviceId,
  characteristicId: characteristicId,
  value: buffer
})
```

### 2. 检测数据处理
```javascript
// 检测结果分析
const analyzeResult = (rawData) => {
  const alContent = parseAluminum(rawData)
  const siContent = parseSilicon(rawData)
  const feContent = parseIron(rawData)
  
  // 品质评级
  const grade = calculateGrade(alContent, siContent, feContent)
  
  return {
    alContent,
    siContent,
    feContent,
    grade
  }
}
```

### 3. 数据可视化
```javascript
// 自定义图表组件
Component({
  methods: {
    drawChart(data) {
      const ctx = this.data.ctx
      // 绘制柱状图、饼图等
      this.drawBarChart(ctx, data)
    }
  }
})
```

## 安装和部署

### 1. 环境准备
- 微信开发者工具
- Node.js 环境
- 微信小程序账号

### 2. 项目配置
```bash
# 克隆项目
git clone [项目地址]

# 安装依赖
npm install

# 配置云开发环境
# 在微信开发者工具中配置云开发环境ID
```

### 3. 云函数部署
```bash
# 部署登录云函数
右键 cloudfunctions/login -> 上传并部署

# 部署数据导出云函数
右键 cloudfunctions/exportData -> 上传并部署
```

### 4. 数据库初始化
在云开发控制台创建以下集合：
- `users` - 用户信息
- `detections` - 检测记录
- `posts` - 论坛帖子
- `comments` - 评论
- `likes` - 点赞记录

## 使用说明

### 1. 用户注册登录
- 首次使用需要微信授权登录
- 支持游客模式（功能受限）

### 2. 设备连接
- 确保检测设备已开启蓝牙
- 在设备页面扫描并连接设备
- 连接成功后可进行检测

### 3. 开始检测
- 在首页点击"开始检测"
- 填写样品信息（可选）
- 等待检测完成查看结果

### 4. 数据管理
- 在历史记录页面查看所有检测数据
- 支持按时间筛选和搜索
- 可导出数据为Excel或PDF格式

### 5. 论坛交流
- 浏览不同分类的帖子
- 发布技术问题或经验分享
- 参与讨论和互动

## 开发团队

本项目由专业的移动应用开发团队开发，具有丰富的小程序开发经验和工业检测领域知识。

## 技术支持

如有技术问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 微信群: [扫码加入技术交流群]

## 版本更新

### v1.0.0 (当前版本)
- ✅ 基础检测功能
- ✅ 设备连接管理
- ✅ 数据存储和导出
- ✅ 交流论坛
- ✅ 用户系统

### 计划更新
- 🔄 AI智能分析
- 🔄 多语言支持
- 🔄 离线检测模式
- 🔄 数据统计分析

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
