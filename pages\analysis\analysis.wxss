.container {
  padding: 20rpx;
  background: linear-gradient(180deg, #f8fafe 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 时间范围选择器 */
.time-range-selector {
  display: flex;
  background: white;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.range-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.range-item.active {
  background: #2B85E4;
  color: white;
}

.range-item text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 概览统计 */
.overview-stats {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  flex: 1;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stat-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.stat-info {
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2B85E4;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 成分分析图表 */
.composition-chart {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.chart-bar {
  flex: 1;
  height: 30rpx;
  background: #f0f0f0;
  border-radius: 15rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 15rpx;
  transition: width 0.8s ease;
}

.chart-label {
  width: 120rpx;
  text-align: right;
}

.element-name {
  display: block;
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.element-value {
  display: block;
  font-size: 20rpx;
  color: #666;
}

/* 质量分布 */
.quality-distribution {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.quality-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
}

.quality-circle {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.quality-count {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.quality-name {
  font-size: 24rpx;
  color: #666;
}

/* 趋势图表 */
.trend-chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 200rpx;
  padding: 20rpx 0;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.trend-bar {
  width: 30rpx;
  height: 150rpx;
  background: #f0f0f0;
  border-radius: 15rpx;
  overflow: hidden;
  display: flex;
  align-items: end;
  margin-bottom: 10rpx;
}

.trend-fill {
  width: 100%;
  background: linear-gradient(180deg, #2B85E4 0%, #1976d2 100%);
  border-radius: 15rpx;
  transition: height 0.8s ease;
}

.trend-date {
  font-size: 20rpx;
  color: #666;
}

/* 详细数据 */
.detail-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #2B85E4;
}

/* 地点统计 */
.location-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.location-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.location-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.location-name {
  font-size: 26rpx;
  color: #333;
}

.location-count {
  font-size: 24rpx;
  color: #2B85E4;
  font-weight: bold;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #2B85E4 0%, #1976d2 100%);
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #2B85E4;
  border: 2rpx solid #2B85E4;
}

.btn-icon {
  font-size: 32rpx;
}

/* 导出对话框 */
.export-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.dialog-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.dialog-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.format-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.format-item:active {
  background: #e3f2fd;
}

.format-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.format-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.format-desc {
  font-size: 24rpx;
  color: #666;
}

.dialog-actions {
  display: flex;
  justify-content: center;
}

.dialog-btn {
  padding: 15rpx 40rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.dialog-btn.cancel {
  background: #f0f0f0;
  color: #666;
}
