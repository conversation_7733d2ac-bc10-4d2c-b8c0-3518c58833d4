.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
}

.logo-section {
  text-align: center;
  margin-top: 80rpx;
  margin-bottom: 100rpx;
}

.logo {
  font-size: 160rpx;
  margin-bottom: 30rpx;
  display: block;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 15rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-section {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 100%;
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.login-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 15rpx;
}

.login-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  position: relative;
}

.wechat-btn {
  background: #07c160;
  color: white;
}

.guest-btn {
  background: #f5f5f5;
  color: #666;
}

.btn-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.features-section {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-top: 60rpx;
  margin-bottom: 40rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.feature-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  display: block;
}

.feature-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

.footer {
  margin-top: auto;
  padding-top: 40rpx;
}

.footer-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}
