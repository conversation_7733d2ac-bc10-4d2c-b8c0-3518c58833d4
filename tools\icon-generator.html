<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2B85E4;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        .icon-item:hover {
            border-color: #2B85E4;
            transform: translateY(-2px);
        }
        .icon-preview {
            width: 78px;
            height: 78px;
            margin: 0 auto 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            cursor: pointer;
        }
        .icon-normal {
            background: #f8f9fa;
            color: #7A7E83;
        }
        .icon-active {
            background: #e3f2fd;
            color: #2B85E4;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .icon-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        .download-btn {
            background: #2B85E4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .download-btn:hover {
            background: #1976d2;
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #2B85E4;
            margin-top: 0;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 导航图标生成器</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            为智能铝土矿检测小程序生成导航栏图标
        </p>

        <div class="icon-grid">
            <div class="icon-item">
                <div class="icon-name">首页</div>
                <div class="icon-preview icon-normal" data-icon="🏠" data-name="home">🏠</div>
                <div class="icon-preview icon-active" data-icon="🏠" data-name="home-active">🏠</div>
                <div class="icon-desc">主页导航图标</div>
                <button class="download-btn" onclick="generateIcon('🏠', 'home', '#7A7E83')">下载普通</button>
                <button class="download-btn" onclick="generateIcon('🏠', 'home-active', '#2B85E4')">下载选中</button>
            </div>

            <div class="icon-item">
                <div class="icon-name">设备</div>
                <div class="icon-preview icon-normal" data-icon="📱" data-name="device">📱</div>
                <div class="icon-preview icon-active" data-icon="📱" data-name="device-active">📱</div>
                <div class="icon-desc">设备管理图标</div>
                <button class="download-btn" onclick="generateIcon('📱', 'device', '#7A7E83')">下载普通</button>
                <button class="download-btn" onclick="generateIcon('📱', 'device-active', '#2B85E4')">下载选中</button>
            </div>

            <div class="icon-item">
                <div class="icon-name">论坛</div>
                <div class="icon-preview icon-normal" data-icon="💬" data-name="forum">💬</div>
                <div class="icon-preview icon-active" data-icon="💬" data-name="forum-active">💬</div>
                <div class="icon-desc">交流论坛图标</div>
                <button class="download-btn" onclick="generateIcon('💬', 'forum', '#7A7E83')">下载普通</button>
                <button class="download-btn" onclick="generateIcon('💬', 'forum-active', '#2B85E4')">下载选中</button>
            </div>

            <div class="icon-item">
                <div class="icon-name">我的</div>
                <div class="icon-preview icon-normal" data-icon="👤" data-name="profile">👤</div>
                <div class="icon-preview icon-active" data-icon="👤" data-name="profile-active">👤</div>
                <div class="icon-desc">个人中心图标</div>
                <button class="download-btn" onclick="generateIcon('👤', 'profile', '#7A7E83')">下载普通</button>
                <button class="download-btn" onclick="generateIcon('👤', 'profile-active', '#2B85E4')">下载选中</button>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li><strong>点击下载按钮</strong> - 生成对应的PNG图标文件</li>
                <li><strong>保存到项目</strong> - 将下载的图标放入 <code>assets/tabbar/</code> 目录</li>
                <li><strong>替换占位符</strong> - 删除现有的占位符文件，使用新生成的图标</li>
                <li><strong>测试效果</strong> - 在微信开发者工具中预览效果</li>
            </ol>
            
            <h3>🎯 图标规范</h3>
            <ul>
                <li><strong>尺寸</strong>: 78x78px (会自动生成156x156px高清版本)</li>
                <li><strong>格式</strong>: PNG格式，透明背景</li>
                <li><strong>颜色</strong>: 普通状态 #7A7E83，选中状态 #2B85E4</li>
                <li><strong>风格</strong>: 简洁现代，符合小程序设计规范</li>
            </ul>

            <h3>💡 优化建议</h3>
            <ul>
                <li>如需更专业的图标，建议使用 Figma 或 Sketch 设计</li>
                <li>可以从 Iconfont 或 Feather Icons 获取矢量图标</li>
                <li>确保图标在不同设备上显示清晰</li>
                <li>保持所有图标的设计风格一致</li>
            </ul>
        </div>
    </div>

    <canvas id="canvas" width="156" height="156"></canvas>

    <script>
        function generateIcon(emoji, filename, color) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 156, 156);
            
            // 设置背景为透明
            ctx.globalCompositeOperation = 'source-over';
            
            // 绘制emoji图标
            ctx.font = '100px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = color;
            
            // 在画布中心绘制emoji
            ctx.fillText(emoji, 78, 78);
            
            // 转换为PNG并下载
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename + '.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }

        // 添加交互效果
        document.querySelectorAll('.icon-preview').forEach(preview => {
            preview.addEventListener('click', function() {
                const icon = this.dataset.icon;
                const name = this.dataset.name;
                const color = this.classList.contains('icon-active') ? '#2B85E4' : '#7A7E83';
                generateIcon(icon, name, color);
            });
        });
    </script>
</body>
</html>
