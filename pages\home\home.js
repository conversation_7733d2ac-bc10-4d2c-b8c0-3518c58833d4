const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    userInfo: null,
    deviceConnected: false,
    currentDevice: null,
    latestResult: null,
    totalDetections: 0,
    todayDetections: 0,
    industryNews: [
      {
        id: 1,
        title: '铝土矿检测技术新突破：AI智能识别准确率达99%',
        summary: '最新研发的人工智能检测技术在铝土矿成分分析中取得重大突破...',
        image: '/assets/images/news1.jpg',
        date: '2024-01-15',
        source: '矿业科技'
      },
      {
        id: 2,
        title: '全球铝土矿市场分析：2024年价格趋势预测',
        summary: '根据最新市场调研，2024年全球铝土矿价格预计将保持稳定增长...',
        image: '/assets/images/news2.jpg',
        date: '2024-01-12',
        source: '行业观察'
      },
      {
        id: 3,
        title: '绿色开采新标准：环保型铝土矿检测设备投入使用',
        summary: '新一代环保型检测设备正式投入市场，大幅降低检测过程中的环境影响...',
        image: '/assets/images/news3.jpg',
        date: '2024-01-10',
        source: '环保资讯'
      }
    ]
  },

  onLoad() {
    this.initPageData()
    this.loadLatestDetection()
    this.loadUserStats()
  },

  onShow() {
    this.updateDeviceStatus()
    this.loadLatestDetection()
  },

  // 初始化页面数据
  initPageData() {
    this.setData({
      userInfo: app.globalData.userInfo,
      deviceConnected: app.globalData.deviceConnected,
      currentDevice: app.globalData.currentDevice
    })
  },

  // 更新设备状态
  updateDeviceStatus() {
    this.setData({
      deviceConnected: app.globalData.deviceConnected,
      currentDevice: app.globalData.currentDevice
    })
  },

  connectDevice() {
    wx.showLoading({ title: '搜索设备中...' })
    wx.startBluetoothDevicesDiscovery({
      success: () => {
        wx.getBluetoothDevices({
          success: res => {
            const devices = res.devices.filter(d => d.name.includes('Bauxite'))
            if (devices.length) {
              app.connectDevice(devices[0].deviceId)
                .then(() => {
                  this.setData({ deviceConnected: true })
                  wx.showToast({ title: '连接成功' })
                })
                .catch(err => {
                  console.error(err)
                  wx.showToast({ title: '连接失败', icon: 'error' })
                })
            } else {
              wx.showToast({ title: '未找到设备', icon: 'error' })
            }
          }
        })
      },
      complete: () => wx.hideLoading()
    })
  },

  startDetection() {
    wx.navigateTo({
      url: '/pages/detection/detection'
    })
  },

  viewHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 导航到设备页面
  goToDevice() {
    wx.switchTab({
      url: '/pages/device/device'
    })
  },

  // 导航到论坛页面
  goToForum() {
    wx.switchTab({
      url: '/pages/forum/forum'
    })
  },

  // 导航到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 导航到帮助页面
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  // 加载最新检测结果
  loadLatestDetection() {
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    if (!openid) return

    db.collection('detections')
      .where({ _openid: openid })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get()
      .then(res => {
        if (res.data.length > 0) {
          const latest = res.data[0]
          this.setData({
            latestResult: {
              ...latest,
              date: this.formatDate(latest.createTime)
            }
          })
        }
      })
      .catch(err => {
        console.error('加载最新检测结果失败:', err)
      })
  },

  // 加载用户统计数据
  loadUserStats() {
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    if (!openid) return

    // 加载总检测次数
    db.collection('detections')
      .where({ _openid: openid })
      .count()
      .then(res => {
        this.setData({ totalDetections: res.total })
      })

    // 加载今日检测次数
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())

    db.collection('detections')
      .where({
        _openid: openid,
        createTime: db.command.gte(startOfDay)
      })
      .count()
      .then(res => {
        this.setData({ todayDetections: res.total })
      })
  },

  // 数据分析
  dataAnalysis() {
    wx.navigateTo({
      url: '/pages/analysis/analysis'
    })
  },

  // 质量报告
  qualityReport() {
    wx.navigateTo({
      url: '/pages/report/report'
    })
  },

  // 查看更多新闻
  viewMoreNews() {
    wx.navigateTo({
      url: '/pages/news/news'
    })
  },

  // 查看新闻详情
  viewNewsDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/news/detail?id=${id}`
    })
  },

  // 格式化日期
  formatDate(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${month}-${day} ${hours}:${minutes}`
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadLatestDetection()
    this.loadUserStats()
    wx.stopPullDownRefresh()
  }
})
