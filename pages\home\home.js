const app = getApp()

Page({
  data: {
    userInfo: null,
    deviceConnected: false,
    alContent: null,
    siContent: null,
    feContent: null
  },

  onLoad() {
    this.setData({
      userInfo: app.globalData.userInfo,
      deviceConnected: app.globalData.deviceConnected
    })
  },

  connectDevice() {
    wx.showLoading({ title: '搜索设备中...' })
    wx.startBluetoothDevicesDiscovery({
      success: () => {
        wx.getBluetoothDevices({
          success: res => {
            const devices = res.devices.filter(d => d.name.includes('Bauxite'))
            if (devices.length) {
              app.connectDevice(devices[0].deviceId)
                .then(() => {
                  this.setData({ deviceConnected: true })
                  wx.showToast({ title: '连接成功' })
                })
                .catch(err => {
                  console.error(err)
                  wx.showToast({ title: '连接失败', icon: 'error' })
                })
            } else {
              wx.showToast({ title: '未找到设备', icon: 'error' })
            }
          }
        })
      },
      complete: () => wx.hideLoading()
    })
  },

  startDetection() {
    wx.navigateTo({
      url: '/pages/detection/detection'
    })
  },

  viewHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 导航到设备页面
  goToDevice() {
    wx.switchTab({
      url: '/pages/device/device'
    })
  },

  // 导航到论坛页面
  goToForum() {
    wx.switchTab({
      url: '/pages/forum/forum'
    })
  },

  // 导航到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 导航到帮助页面
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  }
})
