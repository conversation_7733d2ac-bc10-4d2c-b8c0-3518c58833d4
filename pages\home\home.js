const app = getApp()

Page({
  data: {
    userInfo: null,
    deviceConnected: false,
    alContent: null,
    siContent: null,
    feContent: null
  },

  onLoad() {
    this.setData({
      userInfo: app.globalData.userInfo,
      deviceConnected: app.globalData.deviceConnected
    })
  },

  connectDevice() {
    wx.showLoading({ title: '搜索设备中...' })
    wx.startBluetoothDevicesDiscovery({
      success: () => {
        wx.getBluetoothDevices({
          success: res => {
            const devices = res.devices.filter(d => d.name.includes('Bauxite'))
            if (devices.length) {
              app.connectDevice(devices[0].deviceId)
                .then(() => {
                  this.setData({ deviceConnected: true })
                  wx.showToast({ title: '连接成功' })
                })
                .catch(err => {
                  console.error(err)
                  wx.showToast({ title: '连接失败', icon: 'error' })
                })
            } else {
              wx.showToast({ title: '未找到设备', icon: 'error' })
            }
          }
        })
      },
      complete: () => wx.hideLoading()
    })
  },

  startDetection() {
    if (!this.data.deviceConnected) {
      wx.showToast({ title: '请先连接设备', icon: 'error' })
      return
    }

    wx.showLoading({ title: '检测中...' })
    // 模拟检测结果
    setTimeout(() => {
      this.setData({
        alContent: (Math.random() * 50 + 30).toFixed(2),
        siContent: (Math.random() * 10 + 5).toFixed(2),
        feContent: (Math.random() * 15 + 5).toFixed(2)
      })
      wx.hideLoading()
      wx.showToast({ title: '检测完成' })
    }, 2000)
  },

  viewHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  }
})
