const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    userInfo: null,
    deviceConnected: false,
    currentDevice: null,
    latestResult: null,
    totalDetections: 0,
    todayDetections: 0,
    industryNews: [
      {
        id: 1,
        title: '铝土矿检测技术新突破：AI智能识别准确率达99%',
        summary: '最新研发的人工智能检测技术在铝土矿成分分析中取得重大突破...',
        image: '/assets/images/news1.jpg',
        date: '2024-01-15',
        source: '矿业科技'
      },
      {
        id: 2,
        title: '全球铝土矿市场分析：2024年价格趋势预测',
        summary: '根据最新市场调研，2024年全球铝土矿价格预计将保持稳定增长...',
        image: '/assets/images/news2.jpg',
        date: '2024-01-12',
        source: '行业观察'
      },
      {
        id: 3,
        title: '绿色开采新标准：环保型铝土矿检测设备投入使用',
        summary: '新一代环保型检测设备正式投入市场，大幅降低检测过程中的环境影响...',
        image: '/assets/images/news3.jpg',
        date: '2024-01-10',
        source: '环保资讯'
      }
    ]
  },

  onLoad() {
    this.initPageData()
    this.loadLatestDetection()
    this.loadUserStats()
    this.checkLoginStatus()
  },

  onShow() {
    this.updateDeviceStatus()
    this.loadLatestDetection()
    this.updateUserInfo()
  },

  // 初始化页面数据
  initPageData() {
    this.setData({
      userInfo: app.globalData.userInfo,
      deviceConnected: app.globalData.deviceConnected,
      currentDevice: app.globalData.currentDevice
    })
  },

  // 更新设备状态
  updateDeviceStatus() {
    this.setData({
      deviceConnected: app.globalData.deviceConnected,
      currentDevice: app.globalData.currentDevice
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    const openid = wx.getStorageSync('openid')

    if (userInfo && openid) {
      app.globalData.userInfo = userInfo
      app.globalData.openid = openid
      this.setData({ userInfo })
    }
  },

  // 更新用户信息
  updateUserInfo() {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    }
  },

  // 连接设备
  connectDevice() {
    console.log('connectDevice 方法被调用')

    // 在开发环境中直接使用模拟连接
    if (typeof wx.openBluetoothAdapter === 'undefined') {
      console.log('开发环境，使用模拟连接')
      this.simulateConnection()
      return
    }

    // 检查蓝牙权限
    wx.openBluetoothAdapter({
      success: () => {
        console.log('蓝牙适配器初始化成功')
        this.startDeviceDiscovery()
      },
      fail: (err) => {
        console.error('蓝牙初始化失败:', err)

        // 根据错误类型处理
        if (err.errMsg.includes('INVALID_LOGIN') || err.errMsg.includes('access_token expired')) {
          wx.showModal({
            title: '登录状态异常',
            content: '检测到登录状态异常，将使用模拟连接模式',
            confirmText: '模拟连接',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.simulateConnection()
              }
            }
          })
        } else if (err.errCode === 10001) {
          wx.showModal({
            title: '蓝牙未开启',
            content: '请开启蓝牙后重试，或使用模拟连接',
            confirmText: '模拟连接',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.simulateConnection()
              }
            }
          })
        } else {
          // 其他错误，直接使用模拟连接
          console.log('使用模拟连接模式')
          this.simulateConnection()
        }
      }
    })
  },

  // 开始设备发现
  startDeviceDiscovery() {
    wx.showLoading({ title: '搜索设备中...' })

    wx.startBluetoothDevicesDiscovery({
      success: () => {
        // 模拟搜索到设备并连接
        setTimeout(() => {
          this.connectToDevice()
        }, 2000)
      },
      fail: (err) => {
        console.error('设备搜索失败:', err)
        wx.hideLoading()
        // 模拟连接成功
        this.simulateConnection()
      }
    })
  },

  // 连接到设备
  connectToDevice() {
    wx.hideLoading()
    wx.showLoading({ title: '连接设备中...' })

    // 模拟连接过程
    setTimeout(() => {
      wx.hideLoading()
      this.setData({
        deviceConnected: true,
        currentDevice: 'BAUXITE_DETECTOR_001'
      })
      app.globalData.deviceConnected = true
      app.globalData.currentDevice = 'BAUXITE_DETECTOR_001'

      // 保存连接状态
      wx.setStorageSync('deviceConnected', true)
      wx.setStorageSync('currentDevice', 'BAUXITE_DETECTOR_001')

      wx.showToast({
        title: '设备连接成功',
        icon: 'success'
      })
    }, 1500)
  },

  // 模拟连接（用于开发测试）
  simulateConnection() {
    wx.showLoading({ title: '连接设备中...' })

    setTimeout(() => {
      wx.hideLoading()
      this.setData({
        deviceConnected: true,
        currentDevice: 'BAUXITE_DETECTOR_001'
      })
      app.globalData.deviceConnected = true
      app.globalData.currentDevice = 'BAUXITE_DETECTOR_001'

      wx.setStorageSync('deviceConnected', true)
      wx.setStorageSync('currentDevice', 'BAUXITE_DETECTOR_001')

      wx.showToast({
        title: '设备连接成功',
        icon: 'success'
      })
    }, 1000)
  },

  // 开始检测
  startDetection() {
    console.log('startDetection 方法被调用')

    // 检查设备连接状态
    if (!this.data.deviceConnected) {
      wx.showModal({
        title: '设备未连接',
        content: '请先连接检测设备',
        confirmText: '去连接',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.connectDevice()
          }
        }
      })
      return
    }

    // 检查登录状态
    if (!app.globalData.userInfo && !app.globalData.isGuest) {
      wx.showModal({
        title: '需要登录',
        content: '请先登录以保存检测记录',
        confirmText: '去登录',
        cancelText: '游客模式',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          } else {
            this.startDetectionProcess()
          }
        }
      })
      return
    }

    this.startDetectionProcess()
  },

  // 开始检测流程
  startDetectionProcess() {
    wx.showLoading({ title: '准备检测...' })

    // 模拟设备准备过程
    setTimeout(() => {
      wx.hideLoading()
      wx.navigateTo({
        url: '/pages/detection/detection'
      })
    }, 1500)
  },

  // 查看历史记录
  viewHistory() {
    console.log('viewHistory 方法被调用')

    if (!app.globalData.userInfo && !app.globalData.isGuest) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/history/history',
      success: () => {
        console.log('跳转到历史记录页面成功')
      },
      fail: (err) => {
        console.error('跳转到历史记录页面失败:', err)
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 导航到设备页面
  goToDevice() {
    console.log('goToDevice 方法被调用')

    // 使用全局安全跳转方法
    app.safeNavigate({
      url: '/pages/device/device',
      isTab: true,
      success: () => {
        console.log('跳转到设备页面成功')
      },
      fail: (err) => {
        console.error('最终跳转失败:', err)
        wx.showModal({
          title: '页面跳转异常',
          content: '设备页面暂时无法访问，请重启小程序后重试',
          confirmText: '重启小程序',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.reLaunch({
                url: '/pages/home/<USER>'
              })
            }
          }
        })
      }
    })
  },

  // 导航到论坛页面
  goToForum() {
    console.log('goToForum 方法被调用')

    // 使用全局安全跳转方法
    app.safeNavigate({
      url: '/pages/forum/forum',
      isTab: true,
      success: () => {
        console.log('跳转到论坛页面成功')
      }
    })
  },

  // 导航到设置页面
  goToSettings() {
    console.log('goToSettings 方法被调用')

    // 使用全局安全跳转方法
    app.safeNavigate({
      url: '/pages/settings/settings',
      isTab: false,
      success: () => {
        console.log('跳转到设置页面成功')
      }
    })
  },

  // 导航到帮助页面
  goToHelp() {
    console.log('goToHelp 方法被调用')

    // 使用全局安全跳转方法
    app.safeNavigate({
      url: '/pages/help/help',
      isTab: false,
      success: () => {
        console.log('跳转到帮助页面成功')
      }
    })
  },

  // 加载最新检测结果
  loadLatestDetection() {
    // 直接使用示例数据，避免云数据库调用问题
    this.loadSampleData()
  },

  // 加载示例数据
  loadSampleData() {
    const sampleResult = {
      sampleName: '示例样品 #001',
      alContent: 52.8,
      siContent: 8.3,
      feContent: 15.2,
      tiContent: 2.1,
      moisture: 3.5,
      grade: 'good',
      gradeText: '良好',
      date: '2小时前'
    }

    this.setData({
      latestResult: sampleResult
    })
  },

  // 加载用户统计数据
  loadUserStats() {
    // 使用示例统计数据
    this.setData({
      totalDetections: app.globalData.userInfo ? 15 : 0,
      todayDetections: app.globalData.userInfo ? 3 : 0
    })
  },

  // 数据分析
  dataAnalysis() {
    // 检查是否有检测数据
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    if (!openid) {
      wx.showModal({
        title: '需要登录',
        content: '请先登录以查看数据分析',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }

    wx.showLoading({ title: '加载分析数据...' })

    // 模拟数据分析加载
    setTimeout(() => {
      wx.hideLoading()
      this.showAnalysisModal()
    }, 2000)
  },

  // 显示数据分析模态框
  showAnalysisModal() {
    const analysisData = {
      totalSamples: this.data.totalDetections || 0,
      avgAlContent: 52.3,
      avgSiContent: 8.7,
      avgFeContent: 15.1,
      qualityTrend: '上升',
      recommendation: '建议继续保持当前检测频率'
    }

    wx.showModal({
      title: '数据分析报告',
      content: `总检测样品: ${analysisData.totalSamples}个\n平均铝含量: ${analysisData.avgAlContent}%\n平均硅含量: ${analysisData.avgSiContent}%\n质量趋势: ${analysisData.qualityTrend}\n\n${analysisData.recommendation}`,
      confirmText: '查看详情',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/analysis/analysis'
          })
        }
      }
    })
  },

  // 质量报告
  qualityReport() {
    // 检查登录状态
    if (!app.globalData.userInfo && !app.globalData.isGuest) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showActionSheet({
      itemList: ['生成月度报告', '生成季度报告', '生成年度报告', '自定义报告'],
      success: (res) => {
        const reportTypes = ['monthly', 'quarterly', 'yearly', 'custom']
        const reportType = reportTypes[res.tapIndex]
        this.generateQualityReport(reportType)
      }
    })
  },

  // 生成质量报告
  generateQualityReport(type) {
    const reportNames = {
      monthly: '月度质量报告',
      quarterly: '季度质量报告',
      yearly: '年度质量报告',
      custom: '自定义报告'
    }

    wx.showLoading({ title: `生成${reportNames[type]}中...` })

    // 模拟报告生成
    setTimeout(() => {
      wx.hideLoading()

      const reportData = {
        type: reportNames[type],
        samples: Math.floor(Math.random() * 100) + 20,
        avgQuality: (Math.random() * 20 + 80).toFixed(1),
        passRate: (Math.random() * 10 + 90).toFixed(1),
        generateTime: new Date().toLocaleString()
      }

      wx.showModal({
        title: '报告生成完成',
        content: `${reportData.type}\n检测样品: ${reportData.samples}个\n平均质量: ${reportData.avgQuality}分\n合格率: ${reportData.passRate}%\n生成时间: ${reportData.generateTime}`,
        confirmText: '下载报告',
        cancelText: '关闭',
        success: (res) => {
          if (res.confirm) {
            this.downloadReport(reportData)
          }
        }
      })
    }, 3000)
  },

  // 下载报告
  downloadReport(reportData) {
    wx.showLoading({ title: '准备下载...' })

    // 模拟下载过程
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '报告已保存到相册',
        icon: 'success'
      })
    }, 2000)
  },

  // 查看更多新闻
  viewMoreNews() {
    console.log('viewMoreNews 方法被调用')

    // 使用全局安全跳转方法
    app.safeNavigate({
      url: '/pages/news/news',
      isTab: false,
      success: () => {
        console.log('跳转到新闻页面成功')
      }
    })
  },

  // 查看新闻详情
  viewNewsDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/news/detail?id=${id}`
    })
  },

  // 智能校准
  smartCalibration() {
    // 检查设备连接状态
    if (!this.data.deviceConnected) {
      wx.showModal({
        title: '设备未连接',
        content: '校准功能需要连接检测设备',
        confirmText: '去连接',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.connectDevice()
          }
        }
      })
      return
    }

    wx.showModal({
      title: '智能校准',
      content: '是否开始设备校准？校准过程约需要3-5分钟',
      confirmText: '开始校准',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.startCalibration()
        }
      }
    })
  },

  // 开始校准流程
  startCalibration() {
    wx.showLoading({ title: '校准中...' })

    let progress = 0
    const calibrationSteps = [
      '检查设备状态',
      '校准传感器',
      '调整参数',
      '验证精度',
      '保存设置'
    ]

    const updateProgress = () => {
      if (progress < calibrationSteps.length) {
        wx.showLoading({
          title: `${calibrationSteps[progress]}...`
        })
        progress++
        setTimeout(updateProgress, 2000)
      } else {
        wx.hideLoading()
        wx.showToast({
          title: '校准完成',
          icon: 'success'
        })
      }
    }

    updateProgress()
  },

  // 设备管理
  deviceManagement() {
    wx.switchTab({
      url: '/pages/device/device'
    })
  },

  // 格式化日期
  formatDate(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${month}-${day} ${hours}:${minutes}`
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadLatestDetection()
    this.loadUserStats()
    wx.stopPullDownRefresh()
  },


})
