const app = getApp()

Page({
  data: {
    userInfo: {
      nickName: '',
      avatar: '',
      phone: '',
      email: '',
      company: '',
      position: ''
    },
    loading: false
  },

  onLoad() {
    this.loadUserInfo()
  },

  loadUserInfo() {
    const userInfo = app.globalData.userInfo || {}
    this.setData({
      userInfo: {
        nickName: userInfo.nickName || '',
        avatar: userInfo.avatarUrl || '',
        phone: wx.getStorageSync('userPhone') || '',
        email: wx.getStorageSync('userEmail') || '',
        company: wx.getStorageSync('userCompany') || '',
        position: wx.getStorageSync('userPosition') || ''
      }
    })
  },

  onNickNameInput(e) {
    this.setData({
      'userInfo.nickName': e.detail.value
    })
  },

  onPhoneInput(e) {
    this.setData({
      'userInfo.phone': e.detail.value
    })
  },

  onEmailInput(e) {
    this.setData({
      'userInfo.email': e.detail.value
    })
  },

  onCompanyInput(e) {
    this.setData({
      'userInfo.company': e.detail.value
    })
  },

  onPositionInput(e) {
    this.setData({
      'userInfo.position': e.detail.value
    })
  },

  chooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          'userInfo.avatar': res.tempFilePaths[0]
        })
      }
    })
  },

  saveProfile() {
    const { userInfo } = this.data

    // 验证必填字段
    if (!userInfo.nickName.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    // 保存到本地存储
    wx.setStorageSync('userPhone', userInfo.phone)
    wx.setStorageSync('userEmail', userInfo.email)
    wx.setStorageSync('userCompany', userInfo.company)
    wx.setStorageSync('userPosition', userInfo.position)

    // 更新全局用户信息
    if (app.globalData.userInfo) {
      app.globalData.userInfo.nickName = userInfo.nickName
      app.globalData.userInfo.avatarUrl = userInfo.avatar
      wx.setStorageSync('userInfo', app.globalData.userInfo)
    }

    // 模拟保存过程
    setTimeout(() => {
      this.setData({ loading: false })
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }, 1000)
  },

  resetForm() {
    wx.showModal({
      title: '重置确认',
      content: '确定要重置所有修改吗？',
      success: (res) => {
        if (res.confirm) {
          this.loadUserInfo()
          wx.showToast({
            title: '已重置',
            icon: 'success'
          })
        }
      }
    })
  }
})
