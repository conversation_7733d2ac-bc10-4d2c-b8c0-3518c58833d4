Page({
  data: {
    deviceInfo: {
      name: '智能铝土矿检测仪',
      model: 'BAUXITE-PRO-2024',
      serialNumber: 'BD240115001',
      firmwareVersion: 'v2.1.3',
      hardwareVersion: 'v1.2',
      batteryLevel: 85,
      connectionStatus: '已连接',
      lastCalibration: '2024-01-15 14:30',
      totalDetections: 1256,
      workingHours: 328
    },
    specifications: [
      { label: '检测精度', value: '铝±0.3%, 硅±0.2%, 铁±0.4%' },
      { label: '检测时间', value: '30-60秒' },
      { label: '样品重量', value: '10-500g' },
      { label: '工作温度', value: '-10°C ~ +50°C' },
      { label: '存储温度', value: '-20°C ~ +60°C' },
      { label: '防护等级', value: 'IP65' },
      { label: '电池容量', value: '5000mAh' },
      { label: '充电时间', value: '2-3小时' }
    ]
  },

  onLoad() {
    this.loadDeviceInfo()
  },

  loadDeviceInfo() {
    // 从存储中加载设备信息
    const deviceInfo = wx.getStorageSync('deviceInfo')
    if (deviceInfo) {
      this.setData({ deviceInfo })
    }
  },

  checkFirmwareUpdate() {
    wx.showLoading({ title: '检查更新中...' })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showModal({
        title: '固件更新',
        content: '当前已是最新版本 v2.1.3\n\n更新内容：\n- 提升检测精度\n- 优化电池续航\n- 修复已知问题',
        showCancel: false
      })
    }, 2000)
  },

  exportDeviceReport() {
    wx.showLoading({ title: '生成报告中...' })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '设备报告已生成',
        icon: 'success'
      })
    }, 1500)
  },

  resetDevice() {
    wx.showModal({
      title: '重置设备',
      content: '确定要重置设备吗？这将清除所有设置和校准数据。',
      confirmText: '确定重置',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '重置中...' })
          
          setTimeout(() => {
            wx.hideLoading()
            wx.showToast({
              title: '设备已重置',
              icon: 'success'
            })
          }, 2000)
        }
      }
    })
  },

  contactSupport() {
    wx.showActionSheet({
      itemList: ['在线客服', '电话支持', '邮件反馈'],
      success: (res) => {
        const contacts = [
          '功能开发中',
          '技术支持：400-123-4567',
          '邮箱：<EMAIL>'
        ]
        wx.showToast({
          title: contacts[res.tapIndex],
          icon: 'none',
          duration: 3000
        })
      }
    })
  }
})
