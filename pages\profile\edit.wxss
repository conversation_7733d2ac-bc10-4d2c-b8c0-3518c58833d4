.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.label.required::after {
  content: '*';
  color: #ff4757;
  margin-left: 5rpx;
}

.input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}

.avatar-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 100%;
  height: 100%;
}

.default-avatar {
  font-size: 60rpx;
  color: #999;
}

.avatar-tip {
  font-size: 26rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 0 20rpx;
}

.action-btn {
  flex: 1;
  padding: 30rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background-color: #2B85E4;
  color: white;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}
