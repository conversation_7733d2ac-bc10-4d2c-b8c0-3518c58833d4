/* pages/settings/settings.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 导航栏 */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.header-left {
  display: flex;
  align-items: center;
  color: #666;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 32rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-right: 80rpx;
}

/* 设置区域 */
.section {
  margin: 20rpx 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding: 0 10rpx;
}

.setting-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f8f8;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.item-action {
  font-size: 28rpx;
  color: #2B85E4;
  margin-right: 10rpx;
}

.item-action.danger {
  color: #ff4757;
}

.arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 开关样式优化 */
switch {
  transform: scale(0.8);
}

/* 动画效果 */
.section {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .item-left {
    flex-direction: column;
    align-items: flex-start;
  }

  .item-icon {
    margin-bottom: 10rpx;
  }

  .item-right {
    margin-top: 10rpx;
  }
}

/* 特殊样式 */
.setting-item:has(.danger) {
  background-color: #fff5f5;
}

.setting-item:has(.danger):active {
  background-color: #ffe5e5;
}

/* 存储使用率样式 */
.storage-usage {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.usage-bar {
  flex: 1;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  margin-right: 15rpx;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #2B85E4);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.usage-text {
  font-size: 24rpx;
  color: #999;
}

/* 版本信息样式 */
.version-info {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}

/* 主题色彩变量 */
.theme-default {
  --primary-color: #2B85E4;
  --success-color: #4CAF50;
  --warning-color: #ff9500;
  --danger-color: #ff4757;
}

.theme-dark {
  --primary-color: #1976D2;
  --success-color: #388E3C;
  --warning-color: #F57C00;
  --danger-color: #D32F2F;
}

/* 字体大小变量 */
.font-small {
  --title-size: 28rpx;
  --desc-size: 22rpx;
  --value-size: 24rpx;
}

.font-standard {
  --title-size: 32rpx;
  --desc-size: 26rpx;
  --value-size: 28rpx;
}

.font-large {
  --title-size: 36rpx;
  --desc-size: 30rpx;
  --value-size: 32rpx;
}

.font-xlarge {
  --title-size: 40rpx;
  --desc-size: 34rpx;
  --value-size: 36rpx;
}

/* 应用字体大小 */
.item-title {
  font-size: var(--title-size, 32rpx);
}

.item-desc {
  font-size: var(--desc-size, 26rpx);
}

.item-value {
  font-size: var(--value-size, 28rpx);
}

/* 开发者模式样式 */
.developer-mode .setting-item {
  border-left: 4rpx solid #ff9500;
}

.developer-mode .item-title::after {
  content: " [DEV]";
  color: #ff9500;
  font-size: 20rpx;
  font-weight: normal;
}

/* 性能监控指示器 */
.performance-indicator {
  position: fixed;
  top: 100rpx;
  right: 30rpx;
  background-color: rgba(0,0,0,0.7);
  color: #fff;
  padding: 10rpx 15rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  z-index: 1000;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 16rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #2B85E4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}