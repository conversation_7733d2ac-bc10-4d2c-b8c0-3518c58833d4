// pages/privacy/privacy.js
const app = getApp()

Page({
  data: {
    // 个人信息设置
    profileVisibility: '所有人',
    showAvatar: true,
    showNickname: true,
    
    // 活动隐私
    showOnlineStatus: true,
    showLastActive: false,
    showPostHistory: true,
    
    // 数据使用
    allowAnalytics: true,
    allowPersonalization: true,
    allowPersonalizedAds: false,
    
    // 弹窗状态
    showVisibilityModal: false
  },

  onLoad() {
    this.loadPrivacySettings()
  },

  // 加载隐私设置
  loadPrivacySettings() {
    const privacySettings = wx.getStorageSync('privacy_settings') || {}
    
    this.setData({
      profileVisibility: privacySettings.profileVisibility || '所有人',
      showAvatar: privacySettings.showAvatar !== false,
      showNickname: privacySettings.showNickname !== false,
      showOnlineStatus: privacySettings.showOnlineStatus !== false,
      showLastActive: privacySettings.showLastActive || false,
      showPostHistory: privacySettings.showPostHistory !== false,
      allowAnalytics: privacySettings.allowAnalytics !== false,
      allowPersonalization: privacySettings.allowPersonalization !== false,
      allowPersonalizedAds: privacySettings.allowPersonalizedAds || false
    })
  },

  // 保存隐私设置
  savePrivacySettings() {
    const settings = {
      profileVisibility: this.data.profileVisibility,
      showAvatar: this.data.showAvatar,
      showNickname: this.data.showNickname,
      showOnlineStatus: this.data.showOnlineStatus,
      showLastActive: this.data.showLastActive,
      showPostHistory: this.data.showPostHistory,
      allowAnalytics: this.data.allowAnalytics,
      allowPersonalization: this.data.allowPersonalization,
      allowPersonalizedAds: this.data.allowPersonalizedAds
    }
    
    wx.setStorageSync('privacy_settings', settings)
    console.log('隐私设置已保存:', settings)
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 设置个人资料可见性
  setProfileVisibility() {
    this.setData({ showVisibilityModal: true })
  },

  // 隐藏可见性选择弹窗
  hideVisibilityModal() {
    this.setData({ showVisibilityModal: false })
  },

  // 选择可见性
  selectVisibility(e) {
    const value = e.currentTarget.dataset.value
    this.setData({ 
      profileVisibility: value,
      showVisibilityModal: false
    })
    this.savePrivacySettings()
    
    wx.showToast({
      title: `已设置为${value}`,
      icon: 'success'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 头像显示开关
  onShowAvatarChange(e) {
    this.setData({ showAvatar: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启头像显示' : '已关闭头像显示',
      icon: 'success'
    })
  },

  // 昵称显示开关
  onShowNicknameChange(e) {
    this.setData({ showNickname: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启昵称显示' : '已关闭昵称显示',
      icon: 'success'
    })
  },

  // 在线状态开关
  onOnlineStatusChange(e) {
    this.setData({ showOnlineStatus: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启在线状态' : '已关闭在线状态',
      icon: 'success'
    })
  },

  // 最后活动时间开关
  onLastActiveChange(e) {
    this.setData({ showLastActive: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启活动时间显示' : '已关闭活动时间显示',
      icon: 'success'
    })
  },

  // 帖子历史开关
  onPostHistoryChange(e) {
    this.setData({ showPostHistory: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启帖子历史' : '已关闭帖子历史',
      icon: 'success'
    })
  },

  // 数据分析开关
  onAnalyticsChange(e) {
    this.setData({ allowAnalytics: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已允许数据分析' : '已禁止数据分析',
      icon: 'success'
    })
  },

  // 个性化推荐开关
  onPersonalizationChange(e) {
    this.setData({ allowPersonalization: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启个性化推荐' : '已关闭个性化推荐',
      icon: 'success'
    })
  },

  // 个性化广告开关
  onPersonalizedAdsChange(e) {
    this.setData({ allowPersonalizedAds: e.detail.value })
    this.savePrivacySettings()
    
    wx.showToast({
      title: e.detail.value ? '已开启个性化广告' : '已关闭个性化广告',
      icon: 'success'
    })
  },

  // 下载数据
  downloadData() {
    wx.showModal({
      title: '下载我的数据',
      content: '我们将为您准备数据包，包含您的个人资料、帖子、评论等信息。准备完成后将通过邮件发送给您。',
      confirmText: '申请下载',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '申请中...' })
          
          // 模拟API调用
          setTimeout(() => {
            wx.hideLoading()
            wx.showToast({
              title: '申请已提交',
              icon: 'success'
            })
          }, 2000)
        }
      }
    })
  },

  // 删除数据
  deleteData() {
    wx.showModal({
      title: '删除我的数据',
      content: '⚠️ 警告：此操作将永久删除您的账户和所有数据，包括帖子、评论、个人资料等，且无法恢复。确定要继续吗？',
      confirmText: '确认删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          // 二次确认
          wx.showModal({
            title: '最终确认',
            content: '请再次确认：您真的要永久删除所有数据吗？此操作无法撤销。',
            confirmText: '永久删除',
            confirmColor: '#ff4757',
            success: (res2) => {
              if (res2.confirm) {
                wx.showModal({
                  title: '数据删除',
                  content: '数据删除功能开发中。如需删除数据，请联系客服处理。',
                  showCancel: false
                })
              }
            }
          })
        }
      }
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护。我们收集的信息仅用于提供更好的服务，不会与第三方分享您的个人信息。\n\n详细的隐私政策正在完善中...',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 查看使用条款
  viewTerms() {
    wx.showModal({
      title: '使用条款',
      content: '使用本应用即表示您同意遵守我们的使用条款。请合理使用平台功能，不要发布违法违规内容。\n\n详细的使用条款正在完善中...',
      showCancel: false,
      confirmText: '我知道了'
    })
  }
})
