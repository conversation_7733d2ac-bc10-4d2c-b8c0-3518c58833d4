<view class="container">
  <!-- 设备状态卡片 -->
  <view class="device-status-card">
    <view class="status-header">
      <image class="device-icon" src="/assets/icons/device-{{deviceConnected ? 'connected' : 'disconnected'}}.png" mode="aspectFit"></image>
      <view class="status-info">
        <text class="status-title">设备状态</text>
        <text class="status-text {{deviceConnected ? 'connected' : 'disconnected'}}">
          {{deviceConnected ? '已连接' : '未连接'}}
        </text>
      </view>
    </view>
    <button wx:if="{{!deviceConnected}}" class="connect-btn" bindtap="connectDevice">连接设备</button>
  </view>

  <!-- 检测控制区域 -->
  <view class="detection-control">
    <view class="control-header">
      <text class="section-title">智能检测</text>
      <text class="section-subtitle">精准分析铝土矿成分含量</text>
    </view>

    <!-- 检测按钮 -->
    <view class="detection-button-area" wx:if="{{!detecting && !results.alContent}}">
      <button 
        class="detection-btn {{deviceConnected ? 'active' : 'disabled'}}" 
        bindtap="startDetection"
        disabled="{{!deviceConnected}}"
      >
        <image class="btn-icon" src="/assets/icons/detection.png" mode="aspectFit"></image>
        <text>开始检测</text>
      </button>
      <text class="detection-tip">请确保样品已正确放置在检测区域</text>
    </view>

    <!-- 检测进度 -->
    <view class="detection-progress" wx:if="{{detecting}}">
      <view class="progress-header">
        <text class="progress-title">检测进行中...</text>
        <text class="progress-percent">{{detectionProgress}}%</text>
      </view>
      
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{detectionProgress}}%"></view>
      </view>

      <!-- 检测步骤 -->
      <view class="detection-steps">
        <view 
          class="step-item {{item.status}}"
          wx:for="{{steps}}"
          wx:key="index"
        >
          <view class="step-icon">
            <image wx:if="{{item.status === 'completed'}}" src="/assets/icons/check.png" mode="aspectFit"></image>
            <view wx:elif="{{item.status === 'active'}}" class="loading-dot"></view>
            <text wx:else>{{index + 1}}</text>
          </view>
          <text class="step-name">{{item.name}}</text>
        </view>
      </view>

      <button class="cancel-btn" bindtap="cancelDetection">取消检测</button>
    </view>
  </view>

  <!-- 检测结果 -->
  <view class="detection-results" wx:if="{{results.alContent}}">
    <view class="results-header">
      <text class="section-title">检测结果</text>
      <view class="grade-badge {{results.grade}}">{{results.gradeText}}</view>
    </view>

    <!-- 主要成分 -->
    <view class="main-components">
      <view class="component-item primary">
        <text class="component-name">铝含量 (Al₂O₃)</text>
        <text class="component-value">{{results.alContent}}%</text>
      </view>
      
      <view class="component-grid">
        <view class="component-item">
          <text class="component-name">硅含量</text>
          <text class="component-value">{{results.siContent}}%</text>
        </view>
        <view class="component-item">
          <text class="component-name">铁含量</text>
          <text class="component-value">{{results.feContent}}%</text>
        </view>
        <view class="component-item">
          <text class="component-name">钛含量</text>
          <text class="component-value">{{results.tiContent}}%</text>
        </view>
        <view class="component-item">
          <text class="component-name">水分</text>
          <text class="component-value">{{results.moisture}}%</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="result-actions">
      <button class="action-btn secondary" bindtap="retryDetection">重新检测</button>
      <button class="action-btn primary" bindtap="shareResult">分享结果</button>
    </view>
  </view>

  <!-- 历史记录 -->
  <view class="history-section" wx:if="{{detectionHistory.length > 0}}">
    <view class="section-header">
      <text class="section-title">最近检测</text>
      <text class="view-all" bindtap="viewHistory">查看全部</text>
    </view>

    <view class="history-list">
      <view 
        class="history-item"
        wx:for="{{detectionHistory}}"
        wx:key="_id"
        bindtap="viewDetectionDetail"
        data-id="{{item._id}}"
      >
        <view class="history-info">
          <text class="sample-name">{{item.sampleName}}</text>
          <text class="detection-time">{{item.date}}</text>
        </view>
        <view class="history-result">
          <text class="al-content">Al: {{item.alContent}}%</text>
          <view class="grade-mini {{item.grade}}">{{item.gradeText}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 样品信息输入对话框 -->
  <view class="modal-overlay" wx:if="{{showSampleDialog}}" bindtap="cancelDetection">
    <view class="sample-dialog" catchtap="">
      <view class="dialog-header">
        <text class="dialog-title">样品信息</text>
        <text class="dialog-subtitle">请填写样品基本信息（可选）</text>
      </view>

      <view class="dialog-content">
        <view class="input-group">
          <text class="input-label">样品名称</text>
          <input 
            class="input-field" 
            placeholder="请输入样品名称"
            value="{{sampleInfo.name}}"
            bindinput="onSampleInput"
            data-field="name"
          />
        </view>

        <view class="input-group">
          <text class="input-label">采样位置</text>
          <input 
            class="input-field" 
            placeholder="请输入采样位置"
            value="{{sampleInfo.location}}"
            bindinput="onSampleInput"
            data-field="location"
          />
        </view>

        <view class="input-group">
          <text class="input-label">备注信息</text>
          <textarea 
            class="textarea-field" 
            placeholder="请输入备注信息"
            value="{{sampleInfo.notes}}"
            bindinput="onSampleInput"
            data-field="notes"
          ></textarea>
        </view>
      </view>

      <view class="dialog-actions">
        <button class="dialog-btn cancel" bindtap="cancelDetection">取消</button>
        <button class="dialog-btn confirm" bindtap="confirmDetection">开始检测</button>
      </view>
    </view>
  </view>
</view>
