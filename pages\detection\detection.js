const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    deviceConnected: false,
    detecting: false,
    detectionProgress: 0,
    currentStep: 0,
    steps: [
      { name: '准备检测', status: 'waiting' },
      { name: '数据采集', status: 'waiting' },
      { name: '成分分析', status: 'waiting' },
      { name: '结果生成', status: 'waiting' }
    ],
    results: {
      alContent: null,
      siContent: null,
      feContent: null,
      tiContent: null,
      moisture: null,
      grade: null,
      gradeText: null
    },
    sampleInfo: {
      name: '',
      location: '',
      notes: ''
    },
    showSampleDialog: false,
    detectionHistory: []
  },

  onLoad() {
    this.checkDeviceStatus()
    this.loadDetectionHistory()
  },

  onShow() {
    this.checkDeviceStatus()
  },

  // 检查设备连接状态
  checkDeviceStatus() {
    this.setData({
      deviceConnected: app.globalData.deviceConnected || false
    })
  },

  // 加载检测历史
  loadDetectionHistory() {
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    if (!openid) return

    db.collection('detections')
      .where({ _openid: openid })
      .orderBy('createTime', 'desc')
      .limit(5)
      .get()
      .then(res => {
        this.setData({
          detectionHistory: res.data.map(item => ({
            ...item,
            date: this.formatDate(item.createTime)
          }))
        })
      })
  },

  // 开始检测
  startDetection() {
    if (!this.data.deviceConnected) {
      wx.showModal({
        title: '设备未连接',
        content: '请先连接检测设备',
        confirmText: '去连接',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/device/device'
            })
          }
        }
      })
      return
    }

    // 显示样品信息输入对话框
    this.setData({ showSampleDialog: true })
  },

  // 确认开始检测
  confirmDetection() {
    this.setData({ 
      showSampleDialog: false,
      detecting: true,
      currentStep: 0,
      detectionProgress: 0
    })

    // 重置步骤状态
    const steps = this.data.steps.map(step => ({ ...step, status: 'waiting' }))
    this.setData({ steps })

    // 开始检测流程
    this.executeDetection()
  },

  // 执行检测流程
  executeDetection() {
    const totalSteps = this.data.steps.length
    let currentStep = 0

    const executeStep = () => {
      if (currentStep >= totalSteps) {
        this.completeDetection()
        return
      }

      // 更新当前步骤状态
      const steps = [...this.data.steps]
      steps[currentStep].status = 'active'
      this.setData({ 
        steps,
        currentStep,
        detectionProgress: Math.round((currentStep / totalSteps) * 100)
      })

      // 模拟检测过程
      setTimeout(() => {
        steps[currentStep].status = 'completed'
        this.setData({ steps })
        
        currentStep++
        setTimeout(executeStep, 500)
      }, 2000 + Math.random() * 1000) // 2-3秒随机时间
    }

    executeStep()
  },

  // 完成检测
  completeDetection() {
    // 生成模拟检测结果
    const results = this.generateResults()
    
    this.setData({
      detecting: false,
      detectionProgress: 100,
      results
    })

    // 保存检测结果
    this.saveDetectionResult(results)

    wx.showToast({
      title: '检测完成',
      icon: 'success'
    })
  },

  // 生成检测结果
  generateResults() {
    const alContent = (Math.random() * 20 + 40).toFixed(2) // 40-60%
    const siContent = (Math.random() * 10 + 5).toFixed(2)  // 5-15%
    const feContent = (Math.random() * 15 + 10).toFixed(2) // 10-25%
    const tiContent = (Math.random() * 3 + 1).toFixed(2)   // 1-4%
    const moisture = (Math.random() * 5 + 2).toFixed(2)    // 2-7%

    // 根据铝含量判断品质等级
    let grade, gradeText
    if (alContent >= 55) {
      grade = 'excellent'
      gradeText = '优质'
    } else if (alContent >= 50) {
      grade = 'good'
      gradeText = '良好'
    } else if (alContent >= 45) {
      grade = 'average'
      gradeText = '一般'
    } else {
      grade = 'poor'
      gradeText = '较差'
    }

    return {
      alContent: parseFloat(alContent),
      siContent: parseFloat(siContent),
      feContent: parseFloat(feContent),
      tiContent: parseFloat(tiContent),
      moisture: parseFloat(moisture),
      grade,
      gradeText
    }
  },

  // 保存检测结果
  saveDetectionResult(results) {
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    if (!openid) return

    const detectionData = {
      ...results,
      sampleName: this.data.sampleInfo.name || '未命名样品',
      location: this.data.sampleInfo.location,
      notes: this.data.sampleInfo.notes,
      deviceId: app.globalData.currentDevice,
      createTime: new Date()
    }

    db.collection('detections').add({
      data: detectionData
    }).then(() => {
      console.log('检测结果保存成功')
      this.loadDetectionHistory()
    }).catch(err => {
      console.error('保存检测结果失败', err)
    })
  },

  // 样品信息输入
  onSampleInput(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    this.setData({
      [`sampleInfo.${field}`]: value
    })
  },

  // 取消检测
  cancelDetection() {
    this.setData({ 
      showSampleDialog: false,
      detecting: false,
      currentStep: 0,
      detectionProgress: 0
    })
  },

  // 查看历史记录
  viewHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 查看检测详情
  viewDetectionDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detection/detail?id=${id}`
    })
  },

  // 重新检测
  retryDetection() {
    this.setData({
      results: {
        alContent: null,
        siContent: null,
        feContent: null,
        tiContent: null,
        moisture: null,
        grade: null,
        gradeText: null
      }
    })
    this.startDetection()
  },

  // 分享结果
  shareResult() {
    if (!this.data.results.alContent) {
      wx.showToast({
        title: '暂无检测结果',
        icon: 'none'
      })
      return
    }

    wx.showShareMenu({
      withShareTicket: true
    })
  },

  // 格式化日期
  formatDate(timestamp) {
    const date = new Date(timestamp)
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${month}-${day} ${hours}:${minutes}`
  }
})
