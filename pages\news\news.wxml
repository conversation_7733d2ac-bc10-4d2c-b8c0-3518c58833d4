<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">行业资讯</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon">⏳</view>
    <text>加载中...</text>
  </view>

  <!-- 新闻列表 -->
  <view class="news-list" wx:else>
    <view class="news-item" wx:for="{{newsList}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
      <view class="news-content">
        <view class="news-header">
          <text class="news-title">{{item.title}}</text>
          <view class="news-meta">
            <text class="category">{{item.category}}</text>
            <text class="publish-time">{{item.publishTime}}</text>
          </view>
        </view>
        <text class="news-summary">{{item.summary}}</text>
        <view class="news-footer">
          <view class="views">
            <view class="view-icon">👁️</view>
            <text>{{item.views}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && newsList.length === 0}}">
    <view class="empty-icon">📰</view>
    <text class="empty-text">暂无新闻资讯</text>
  </view>
</view>
