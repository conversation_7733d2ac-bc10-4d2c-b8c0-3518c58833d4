const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    loading: true,
    timeRange: 'month', // week, month, quarter, year
    analysisData: {
      totalSamples: 0,
      avgAlContent: 0,
      avgSiContent: 0,
      avgFeContent: 0,
      avgTiContent: 0,
      avgMoisture: 0,
      qualityDistribution: {
        excellent: 0,
        good: 0,
        average: 0,
        poor: 0
      },
      trendData: [],
      topLocations: []
    },
    chartData: {
      composition: [],
      trend: [],
      quality: []
    },
    showExportDialog: false
  },

  onLoad() {
    this.loadAnalysisData()
  },

  onShow() {
    this.loadAnalysisData()
  },

  // 加载分析数据
  loadAnalysisData() {
    this.setData({ loading: true })

    const openid = app.globalData.openid || wx.getStorageSync('openid')
    if (!openid) {
      this.showLoginPrompt()
      return
    }

    // 计算时间范围
    const timeFilter = this.getTimeFilter()
    
    db.collection('detections')
      .where({
        _openid: openid,
        createTime: db.command.gte(timeFilter.startTime)
      })
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        this.processAnalysisData(res.data)
      })
      .catch(err => {
        console.error('加载分析数据失败:', err)
        this.loadSampleData()
      })
  },

  // 显示登录提示
  showLoginPrompt() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录以查看数据分析',
      confirmText: '去登录',
      cancelText: '查看示例',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        } else {
          this.loadSampleData()
        }
      }
    })
  },

  // 加载示例数据
  loadSampleData() {
    const sampleData = this.generateSampleData()
    this.processAnalysisData(sampleData)
  },

  // 生成示例数据
  generateSampleData() {
    const samples = []
    const now = new Date()
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      samples.push({
        alContent: Math.random() * 20 + 45,
        siContent: Math.random() * 10 + 5,
        feContent: Math.random() * 15 + 10,
        tiContent: Math.random() * 3 + 1,
        moisture: Math.random() * 5 + 2,
        grade: ['excellent', 'good', 'average', 'poor'][Math.floor(Math.random() * 4)],
        location: ['矿区A', '矿区B', '矿区C'][Math.floor(Math.random() * 3)],
        createTime: date
      })
    }
    
    return samples
  },

  // 处理分析数据
  processAnalysisData(rawData) {
    if (rawData.length === 0) {
      this.setData({
        loading: false,
        analysisData: {
          totalSamples: 0,
          avgAlContent: 0,
          avgSiContent: 0,
          avgFeContent: 0,
          avgTiContent: 0,
          avgMoisture: 0,
          qualityDistribution: { excellent: 0, good: 0, average: 0, poor: 0 },
          trendData: [],
          topLocations: []
        }
      })
      return
    }

    // 计算平均值
    const totalSamples = rawData.length
    const avgAlContent = (rawData.reduce((sum, item) => sum + item.alContent, 0) / totalSamples).toFixed(2)
    const avgSiContent = (rawData.reduce((sum, item) => sum + item.siContent, 0) / totalSamples).toFixed(2)
    const avgFeContent = (rawData.reduce((sum, item) => sum + item.feContent, 0) / totalSamples).toFixed(2)
    const avgTiContent = (rawData.reduce((sum, item) => sum + item.tiContent, 0) / totalSamples).toFixed(2)
    const avgMoisture = (rawData.reduce((sum, item) => sum + item.moisture, 0) / totalSamples).toFixed(2)

    // 计算质量分布
    const qualityDistribution = {
      excellent: rawData.filter(item => item.grade === 'excellent').length,
      good: rawData.filter(item => item.grade === 'good').length,
      average: rawData.filter(item => item.grade === 'average').length,
      poor: rawData.filter(item => item.grade === 'poor').length
    }

    // 生成趋势数据
    const trendData = this.generateTrendData(rawData)

    // 统计热门地点
    const locationStats = {}
    rawData.forEach(item => {
      if (item.location) {
        locationStats[item.location] = (locationStats[item.location] || 0) + 1
      }
    })
    const topLocations = Object.entries(locationStats)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([location, count]) => ({ location, count }))

    // 生成图表数据
    const chartData = this.generateChartData({
      avgAlContent, avgSiContent, avgFeContent, avgTiContent, avgMoisture
    }, qualityDistribution, trendData)

    this.setData({
      loading: false,
      analysisData: {
        totalSamples,
        avgAlContent: parseFloat(avgAlContent),
        avgSiContent: parseFloat(avgSiContent),
        avgFeContent: parseFloat(avgFeContent),
        avgTiContent: parseFloat(avgTiContent),
        avgMoisture: parseFloat(avgMoisture),
        qualityDistribution,
        trendData,
        topLocations
      },
      chartData
    })
  },

  // 生成趋势数据
  generateTrendData(rawData) {
    const groupedData = {}
    
    rawData.forEach(item => {
      const date = new Date(item.createTime)
      const key = `${date.getMonth() + 1}-${date.getDate()}`
      
      if (!groupedData[key]) {
        groupedData[key] = []
      }
      groupedData[key].push(item.alContent)
    })

    return Object.entries(groupedData)
      .map(([date, values]) => ({
        date,
        avgAlContent: (values.reduce((sum, val) => sum + val, 0) / values.length).toFixed(2)
      }))
      .sort((a, b) => new Date(a.date) - new Date(b.date))
      .slice(-7) // 最近7天
  },

  // 生成图表数据
  generateChartData(avgData, qualityData, trendData) {
    return {
      composition: [
        { name: '铝(Al)', value: avgData.avgAlContent, color: '#2B85E4' },
        { name: '硅(Si)', value: avgData.avgSiContent, color: '#FF6B6B' },
        { name: '铁(Fe)', value: avgData.avgFeContent, color: '#4ECDC4' },
        { name: '钛(Ti)', value: avgData.avgTiContent, color: '#45B7D1' },
        { name: '水分', value: avgData.avgMoisture, color: '#96CEB4' }
      ],
      quality: [
        { name: '优秀', value: qualityData.excellent, color: '#2ed573' },
        { name: '良好', value: qualityData.good, color: '#ffa502' },
        { name: '一般', value: qualityData.average, color: '#ff6348' },
        { name: '较差', value: qualityData.poor, color: '#ff4757' }
      ],
      trend: trendData
    }
  },

  // 获取时间过滤器
  getTimeFilter() {
    const now = new Date()
    let startTime

    switch (this.data.timeRange) {
      case 'week':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'month':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case 'quarter':
        startTime = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'year':
        startTime = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    return { startTime, endTime: now }
  },

  // 切换时间范围
  switchTimeRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ timeRange: range })
    this.loadAnalysisData()
  },

  // 导出分析报告
  exportReport() {
    this.setData({ showExportDialog: true })
  },

  // 确认导出
  confirmExport(e) {
    const format = e.currentTarget.dataset.format
    this.setData({ showExportDialog: false })
    
    wx.showLoading({ title: '生成报告中...' })
    
    // 模拟报告生成
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: `${format.toUpperCase()}报告已生成`,
        icon: 'success'
      })
    }, 3000)
  },

  // 取消导出
  cancelExport() {
    this.setData({ showExportDialog: false })
  },

  // 查看详细数据
  viewDetailData() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 分享分析结果
  shareAnalysis() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  }
})
