Page({
  data: {
    deviceSettings: {
      autoCalibration: true,
      detectionMode: 'standard',
      sampleSize: 'medium',
      dataSync: true,
      powerSaving: false
    },
    detectionModes: [
      { value: 'fast', name: '快速模式' },
      { value: 'standard', name: '标准模式' },
      { value: 'precise', name: '精确模式' }
    ],
    sampleSizes: [
      { value: 'small', name: '小样品' },
      { value: 'medium', name: '中等样品' },
      { value: 'large', name: '大样品' }
    ],
    detectionModeIndex: 1,
    detectionModeName: '标准模式',
    sampleSizeIndex: 1,
    sampleSizeName: '中等样品'
  },

  onLoad() {
    this.loadSettings()
  },

  loadSettings() {
    const settings = wx.getStorageSync('deviceSettings') || this.data.deviceSettings
    this.updatePickerData(settings)
    this.setData({ deviceSettings: settings })
  },

  updatePickerData(settings) {
    // 更新检测模式
    const detectionModeIndex = this.data.detectionModes.findIndex(item => item.value === settings.detectionMode)
    const detectionModeName = this.data.detectionModes[detectionModeIndex]?.name || '标准模式'

    // 更新样品大小
    const sampleSizeIndex = this.data.sampleSizes.findIndex(item => item.value === settings.sampleSize)
    const sampleSizeName = this.data.sampleSizes[sampleSizeIndex]?.name || '中等样品'

    this.setData({
      detectionModeIndex: detectionModeIndex >= 0 ? detectionModeIndex : 1,
      detectionModeName,
      sampleSizeIndex: sampleSizeIndex >= 0 ? sampleSizeIndex : 1,
      sampleSizeName
    })
  },

  saveSettings() {
    wx.setStorageSync('deviceSettings', this.data.deviceSettings)
    wx.showToast({
      title: '设置已保存',
      icon: 'success'
    })
  },

  toggleAutoCalibration(e) {
    this.setData({
      'deviceSettings.autoCalibration': e.detail.value
    })
    this.saveSettings()
  },

  onDetectionModeChange(e) {
    const index = e.detail.value
    const selectedMode = this.data.detectionModes[index]
    this.setData({
      'deviceSettings.detectionMode': selectedMode.value,
      detectionModeIndex: index,
      detectionModeName: selectedMode.name
    })
    this.saveSettings()
  },

  onSampleSizeChange(e) {
    const index = e.detail.value
    const selectedSize = this.data.sampleSizes[index]
    this.setData({
      'deviceSettings.sampleSize': selectedSize.value,
      sampleSizeIndex: index,
      sampleSizeName: selectedSize.name
    })
    this.saveSettings()
  },

  toggleDataSync(e) {
    this.setData({
      'deviceSettings.dataSync': e.detail.value
    })
    this.saveSettings()
  },

  togglePowerSaving(e) {
    this.setData({
      'deviceSettings.powerSaving': e.detail.value
    })
    this.saveSettings()
  },

  resetSettings() {
    wx.showModal({
      title: '重置设置',
      content: '确定要恢复默认设置吗？',
      success: (res) => {
        if (res.confirm) {
          const defaultSettings = {
            autoCalibration: true,
            detectionMode: 'standard',
            sampleSize: 'medium',
            dataSync: true,
            powerSaving: false
          }
          this.updatePickerData(defaultSettings)
          this.setData({ deviceSettings: defaultSettings })
          this.saveSettings()
        }
      }
    })
  }
})
