Page({
  data: {
    deviceSettings: {
      autoCalibration: true,
      detectionMode: 'standard',
      sampleSize: 'medium',
      dataSync: true,
      powerSaving: false
    },
    detectionModes: [
      { value: 'fast', name: '快速模式' },
      { value: 'standard', name: '标准模式' },
      { value: 'precise', name: '精确模式' }
    ],
    sampleSizes: [
      { value: 'small', name: '小样品' },
      { value: 'medium', name: '中等样品' },
      { value: 'large', name: '大样品' }
    ]
  },

  onLoad() {
    this.loadSettings()
  },

  loadSettings() {
    const settings = wx.getStorageSync('deviceSettings') || this.data.deviceSettings
    this.setData({ deviceSettings: settings })
  },

  saveSettings() {
    wx.setStorageSync('deviceSettings', this.data.deviceSettings)
    wx.showToast({
      title: '设置已保存',
      icon: 'success'
    })
  },

  toggleAutoCalibration(e) {
    this.setData({
      'deviceSettings.autoCalibration': e.detail.value
    })
    this.saveSettings()
  },

  onDetectionModeChange(e) {
    const index = e.detail.value
    this.setData({
      'deviceSettings.detectionMode': this.data.detectionModes[index].value
    })
    this.saveSettings()
  },

  onSampleSizeChange(e) {
    const index = e.detail.value
    this.setData({
      'deviceSettings.sampleSize': this.data.sampleSizes[index].value
    })
    this.saveSettings()
  },

  toggleDataSync(e) {
    this.setData({
      'deviceSettings.dataSync': e.detail.value
    })
    this.saveSettings()
  },

  togglePowerSaving(e) {
    this.setData({
      'deviceSettings.powerSaving': e.detail.value
    })
    this.saveSettings()
  },

  resetSettings() {
    wx.showModal({
      title: '重置设置',
      content: '确定要恢复默认设置吗？',
      success: (res) => {
        if (res.confirm) {
          const defaultSettings = {
            autoCalibration: true,
            detectionMode: 'standard',
            sampleSize: 'medium',
            dataSync: true,
            powerSaving: false
          }
          this.setData({ deviceSettings: defaultSettings })
          this.saveSettings()
        }
      }
    })
  }
})
