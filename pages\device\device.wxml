<view class="container">
  <!-- 当前连接设备 -->
  <view class="current-device" wx:if="{{currentDevice}}">
    <view class="device-card connected">
      <view class="device-info">
        <image class="device-icon" src="/assets/icons/device-connected.png" mode="aspectFit"></image>
        <view class="device-details">
          <text class="device-name">智能检测仪</text>
          <text class="device-id">设备ID: {{currentDevice}}</text>
          <text class="connection-status">已连接</text>
        </view>
      </view>
      <view class="device-actions">
        <button class="action-btn" bindtap="startDetection">开始检测</button>
        <button class="action-btn secondary" bindtap="disconnectDevice">断开连接</button>
      </view>
    </view>
  </view>

  <!-- 设备扫描区域 -->
  <view class="scan-section">
    <view class="section-header">
      <text class="section-title">可用设备</text>
      <button
        class="scan-btn {{scanning ? 'scanning' : ''}}"
        bindtap="startScan"
        disabled="{{scanning}}"
      >
        <image class="scan-icon" src="/assets/icons/{{scanning ? 'scanning' : 'scan'}}.png" mode="aspectFit"></image>
        <text>{{scanning ? '扫描中...' : '扫描设备'}}</text>
      </button>
    </view>

    <!-- 设备列表 -->
    <view class="device-list">
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{devices.length === 0 && !scanning}}">
        <image class="empty-icon" src="/assets/icons/no-device.png" mode="aspectFit"></image>
        <text class="empty-text">未发现可用设备</text>
        <text class="empty-hint">请确保设备已开启并处于可连接状态</text>
      </view>

      <!-- 设备项 -->
      <view
        class="device-item {{currentDevice === item.deviceId ? 'active' : ''}}"
        wx:for="{{devices}}"
        wx:key="deviceId"
        bindtap="handleConnect"
        data-device="{{item}}"
      >
        <view class="device-info">
          <view class="device-icon">📱</view>
          <view class="device-details">
            <text class="device-name">{{item.name}}</text>
            <text class="device-id">{{item.deviceId}}</text>
            <view class="signal-strength">
              <text class="signal-label">信号强度:</text>
              <view class="signal-bars">
                <view class="signal-bar {{item.RSSI > -60 ? 'active' : ''}}"></view>
                <view class="signal-bar {{item.RSSI > -70 ? 'active' : ''}}"></view>
                <view class="signal-bar {{item.RSSI > -80 ? 'active' : ''}}"></view>
                <view class="signal-bar {{item.RSSI > -90 ? 'active' : ''}}"></view>
              </view>
              <text class="signal-value">{{item.RSSI}}dBm</text>
            </view>
          </view>
        </view>
        <view class="connect-status">
          <text wx:if="{{currentDevice === item.deviceId}}" class="connected-text">已连接</text>
          <button wx:else class="connect-btn" catchtap="handleConnect" data-device="{{item}}">连接</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 设备管理 -->
  <view class="device-management">
    <view class="section-header">
      <text class="section-title">设备管理</text>
    </view>

    <view class="management-options">
      <view class="option-item" bindtap="deviceSettings">
        <view class="option-icon">⚙️</view>
        <text class="option-text">设备设置</text>
        <view class="arrow-icon">▶</view>
      </view>

      <view class="option-item" bindtap="deviceCalibration">
        <view class="option-icon">🎯</view>
        <text class="option-text">设备校准</text>
        <view class="arrow-icon">▶</view>
      </view>

      <view class="option-item" bindtap="deviceInfo">
        <view class="option-icon">ℹ️</view>
        <text class="option-text">设备信息</text>
        <view class="arrow-icon">▶</view>
      </view>

      <view class="option-item" bindtap="deviceHelp">
        <view class="option-icon">❓</view>
        <text class="option-text">使用帮助</text>
        <view class="arrow-icon">▶</view>
      </view>
    </view>
  </view>

  <!-- 连接提示 -->
  <view class="connection-tips">
    <view class="tips-header">
      <view class="tips-icon">💡</view>
      <text class="tips-title">连接提示</text>
    </view>
    <view class="tips-content">
      <text class="tip-item">• 确保设备电量充足</text>
      <text class="tip-item">• 保持设备与手机距离在10米以内</text>
      <text class="tip-item">• 避免在强电磁干扰环境中使用</text>
      <text class="tip-item">• 首次连接可能需要较长时间</text>
    </view>
  </view>
</view>