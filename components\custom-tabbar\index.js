Component({
  data: {
    selected: 0,
    color: "#7A7E83",
    selectedColor: "#2B85E4",
    list: [
      {
        pagePath: "/pages/home/<USER>",
        text: "首页",
        icon: "🏠",
        selectedIcon: "🏠"
      },
      {
        pagePath: "/pages/device/device",
        text: "设备",
        icon: "📱",
        selectedIcon: "📱"
      },
      {
        pagePath: "/pages/forum/forum",
        text: "论坛",
        icon: "💬",
        selectedIcon: "💬"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        icon: "👤",
        selectedIcon: "👤"
      }
    ]
  },

  lifetimes: {
    attached() {
      this.updateSelected()
    }
  },

  pageLifetimes: {
    show() {
      this.updateSelected()
    }
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      const index = data.index

      console.log('switchTab clicked:', url, index)

      // 防止重复点击
      if (this.data.selected === parseInt(index)) {
        console.log('Already selected, ignore click')
        return
      }

      // 立即更新选中状态
      this.setData({
        selected: parseInt(index)
      })

      // 跳转页面
      wx.switchTab({
        url: url,
        success: () => {
          console.log('switchTab success:', url)
        },
        fail: (err) => {
          console.error('switchTab failed:', err)
          // 如果跳转失败，恢复原来的选中状态
          this.updateSelected()
        }
      })
    },

    updateSelected() {
      // 获取当前页面路径
      const pages = getCurrentPages()
      if (pages.length === 0) return

      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route

      console.log('Current route:', currentRoute)

      // 设置当前选中的tab
      this.setSelected(currentRoute)
    },

    setSelected(currentRoute) {
      const list = this.data.list
      let selectedIndex = 0

      for (let i = 0; i < list.length; i++) {
        // 移除开头的 '/' 进行比较
        const pagePath = list[i].pagePath.substring(1)
        if (pagePath === currentRoute) {
          selectedIndex = i
          break
        }
      }

      console.log('Set selected index:', selectedIndex)

      this.setData({
        selected: selectedIndex
      })
    }
  }
})
