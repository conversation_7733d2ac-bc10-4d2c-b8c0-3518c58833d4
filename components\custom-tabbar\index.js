Component({
  data: {
    selected: 0,
    color: "#7A7E83",
    selectedColor: "#2B85E4",
    list: [
      {
        pagePath: "/pages/home/<USER>",
        text: "首页",
        icon: "🏠",
        selectedIcon: "🏠"
      },
      {
        pagePath: "/pages/device/device", 
        text: "设备",
        icon: "📱",
        selectedIcon: "📱"
      },
      {
        pagePath: "/pages/forum/forum",
        text: "论坛", 
        icon: "💬",
        selectedIcon: "💬"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        icon: "👤", 
        selectedIcon: "👤"
      }
    ]
  },

  attached() {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage.route
    
    // 设置当前选中的tab
    this.setSelected(currentRoute)
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      
      wx.switchTab({ url })
      this.setSelected(url.substring(1)) // 移除开头的 '/'
    },

    setSelected(currentRoute) {
      const list = this.data.list
      for (let i = 0; i < list.length; i++) {
        if (list[i].pagePath.substring(1) === currentRoute) {
          this.setData({
            selected: i
          })
          break
        }
      }
    }
  }
})
