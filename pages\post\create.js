// pages/post/create.js
const app = getApp()

Page({
  data: {
    // 帖子基本信息
    title: '',
    content: '',
    selectedCategory: '',

    // 分类列表
    categories: [
      { id: 'tech', name: '技术交流', icon: '🔧' },
      { id: 'experience', name: '经验分享', icon: '💡' },
      { id: 'question', name: '问题求助', icon: '❓' },
      { id: 'news', name: '行业资讯', icon: '📰' },
      { id: 'equipment', name: '设备讨论', icon: '⚙️' },
      { id: 'other', name: '其他', icon: '💬' }
    ],

    // 图片相关
    images: [],

    // 标签相关
    tags: [],
    currentTag: '',
    suggestedTags: ['铝土矿', '检测技术', '设备维护', '数据分析', '质量控制', '经验分享'],

    // 发布设置
    isAnonymous: false,
    allowComments: true,

    // 状态控制
    canPublish: false,
    publishing: false
  },

  onLoad(options) {
    console.log('发帖页面加载')
    this.checkCanPublish()
  },

  // 返回上一页
  goBack() {
    if (this.data.title || this.data.content) {
      wx.showModal({
        title: '确认离开',
        content: '您有未保存的内容，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack()
          }
        }
      })
    } else {
      wx.navigateBack()
    }
  },

  // 选择分类
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    this.setData({
      selectedCategory: categoryId
    })
    this.checkCanPublish()
  },

  // 标题输入
  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    })
    this.checkCanPublish()
  },

  // 内容输入
  onContentInput(e) {
    this.setData({
      content: e.detail.value
    })
    this.checkCanPublish()
  },

  // 检查是否可以发布
  checkCanPublish() {
    const { title, content, selectedCategory } = this.data
    const canPublish = title.trim().length > 0 &&
                      content.trim().length > 0 &&
                      selectedCategory !== ''

    this.setData({ canPublish })
  },

  // 选择图片
  chooseImage() {
    const remainingCount = 9 - this.data.images.length

    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => file.tempFilePath)
        this.setData({
          images: [...this.data.images, ...tempFiles]
        })

        wx.showToast({
          title: `已添加${tempFiles.length}张图片`,
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        })
      }
    })
  },

  // 删除图片
  deleteImage(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.images
    images.splice(index, 1)

    this.setData({ images })

    wx.showToast({
      title: '图片已删除',
      icon: 'success'
    })
  },

  // 标签输入
  onTagInput(e) {
    this.setData({
      currentTag: e.detail.value
    })
  },

  // 添加标签
  addTag() {
    const tag = this.data.currentTag.trim()
    if (!tag) return

    if (this.data.tags.includes(tag)) {
      wx.showToast({
        title: '标签已存在',
        icon: 'none'
      })
      return
    }

    if (this.data.tags.length >= 5) {
      wx.showToast({
        title: '最多添加5个标签',
        icon: 'none'
      })
      return
    }

    this.setData({
      tags: [...this.data.tags, tag],
      currentTag: ''
    })
  },

  // 添加推荐标签
  addSuggestedTag(e) {
    const tag = e.currentTarget.dataset.tag

    if (this.data.tags.includes(tag)) {
      wx.showToast({
        title: '标签已存在',
        icon: 'none'
      })
      return
    }

    if (this.data.tags.length >= 5) {
      wx.showToast({
        title: '最多添加5个标签',
        icon: 'none'
      })
      return
    }

    this.setData({
      tags: [...this.data.tags, tag]
    })
  },

  // 移除标签
  removeTag(e) {
    const index = e.currentTarget.dataset.index
    const tags = this.data.tags
    tags.splice(index, 1)

    this.setData({ tags })
  },

  // 切换匿名发布
  toggleAnonymous(e) {
    this.setData({
      isAnonymous: e.detail.value
    })
  },

  // 切换允许评论
  toggleComments(e) {
    this.setData({
      allowComments: e.detail.value
    })
  },

  // 发布帖子
  async publishPost() {
    if (!this.data.canPublish || this.data.publishing) {
      return
    }

    this.setData({ publishing: true })

    try {
      // 显示发布中提示
      wx.showLoading({
        title: '发布中...',
        mask: true
      })

      // 上传图片
      const imageUrls = await this.uploadImages()

      // 构建帖子数据
      const postData = this.buildPostData(imageUrls)

      // 保存帖子
      const postId = await this.savePost(postData)

      wx.hideLoading()

      // 发布成功
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      })

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        // 返回到论坛页面，而不是跳转到详情页
        wx.navigateBack({
          delta: 1,
          success: () => {
            console.log('返回论坛页面成功')
            // 通知论坛页面刷新数据
            const pages = getCurrentPages()
            if (pages.length > 0) {
              const prevPage = pages[pages.length - 1]
              if (prevPage.route === 'pages/forum/forum' && prevPage.refreshPosts) {
                prevPage.refreshPosts()
              }
            }
          },
          fail: () => {
            // 如果返回失败，跳转到论坛页面
            wx.switchTab({
              url: '/pages/forum/forum'
            })
          }
        })
      }, 1500)

    } catch (error) {
      console.error('发布帖子失败:', error)
      wx.hideLoading()

      wx.showModal({
        title: '发布失败',
        content: error.message || '网络异常，请重试',
        showCancel: false
      })
    } finally {
      this.setData({ publishing: false })
    }
  },

  // 上传图片
  async uploadImages() {
    if (this.data.images.length === 0) {
      return []
    }

    const uploadPromises = this.data.images.map((imagePath, index) => {
      return new Promise((resolve, reject) => {
        // 在开发环境中，模拟上传成功
        const systemInfo = wx.getSystemInfoSync()
        const isDevelopment = systemInfo.platform === 'devtools'

        if (isDevelopment) {
          console.log(`模拟上传图片 ${index + 1}`)
          setTimeout(() => {
            resolve(`https://mock-image-${index + 1}-${Date.now()}.jpg`)
          }, 500)
          return
        }

        // 真实环境中的上传逻辑
        wx.uploadFile({
          url: 'https://your-api-domain.com/upload/image',
          filePath: imagePath,
          name: 'image',
          header: {
            'Authorization': `Bearer ${app.globalData.token || ''}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.success) {
                resolve(data.imageUrl)
              } else {
                reject(new Error(data.message || '图片上传失败'))
              }
            } catch (e) {
              reject(new Error('图片上传响应解析失败'))
            }
          },
          fail: (err) => {
            reject(new Error(`图片上传失败: ${err.errMsg}`))
          }
        })
      })
    })

    return Promise.all(uploadPromises)
  },

  // 构建帖子数据
  buildPostData(imageUrls) {
    const userInfo = app.globalData.userInfo
    const isGuest = app.globalData.isGuest

    return {
      id: 'post_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      title: this.data.title.trim(),
      content: this.data.content.trim(),
      category: this.data.selectedCategory,
      images: imageUrls,
      tags: this.data.tags,
      isAnonymous: this.data.isAnonymous,
      allowComments: this.data.allowComments,
      author: this.data.isAnonymous ? {
        id: 'anonymous',
        nickname: '匿名用户',
        avatar: '/images/anonymous-avatar.png'
      } : {
        id: isGuest ? 'guest' : (app.globalData.openid || 'user_' + Date.now()),
        nickname: isGuest ? '游客用户' : (userInfo?.nickName || '用户'),
        avatar: isGuest ? '/images/guest-avatar.png' : (userInfo?.avatarUrl || '/images/default-avatar.png')
      },
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      viewCount: 0,
      likeCount: 0,
      commentCount: 0,
      status: 'published'
    }
  },

  // 保存帖子
  async savePost(postData) {
    return new Promise((resolve, reject) => {
      // 检查是否在开发环境
      const systemInfo = wx.getSystemInfoSync()
      const isDevelopment = systemInfo.platform === 'devtools'

      if (isDevelopment) {
        console.log('开发环境，模拟保存帖子:', postData)

        // 保存到本地存储（模拟数据库）
        this.saveToLocalStorage(postData)

        setTimeout(() => {
          resolve(postData.id)
        }, 1000)
        return
      }

      // 真实环境中的保存逻辑
      wx.request({
        url: 'https://your-api-domain.com/api/posts',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${app.globalData.token || ''}`
        },
        data: postData,
        success: (res) => {
          if (res.data.success) {
            resolve(res.data.postId)
          } else {
            reject(new Error(res.data.message || '保存帖子失败'))
          }
        },
        fail: (err) => {
          reject(new Error(`网络请求失败: ${err.errMsg}`))
        }
      })
    })
  },

  // 保存到本地存储（开发环境使用）
  saveToLocalStorage(postData) {
    try {
      // 获取现有帖子列表
      let posts = wx.getStorageSync('forum_posts') || []

      // 添加新帖子到列表开头
      posts.unshift(postData)

      // 限制本地存储的帖子数量（最多100个）
      if (posts.length > 100) {
        posts = posts.slice(0, 100)
      }

      // 保存到本地存储
      wx.setStorageSync('forum_posts', posts)

      console.log('帖子已保存到本地存储:', postData.id)
    } catch (error) {
      console.error('保存到本地存储失败:', error)
      throw new Error('保存帖子失败')
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '发布帖子 - 智能铝土矿检测论坛',
      path: '/pages/post/create'
    }
  }
})