const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { type, data } = event

  try {
    let fileContent = ''
    let fileName = ''
    let contentType = ''

    switch (type) {
      case 'excel':
        // 生成CSV格式（简化的Excel）
        fileContent = generateCSV(data)
        fileName = `detection_data_${Date.now()}.csv`
        contentType = 'text/csv'
        break
      
      case 'pdf':
        // 这里可以集成PDF生成库
        fileContent = generateTextReport(data)
        fileName = `detection_report_${Date.now()}.txt`
        contentType = 'text/plain'
        break
      
      default:
        throw new Error('不支持的导出类型')
    }

    // 上传文件到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: `exports/${wxContext.OPENID}/${fileName}`,
      fileContent: Buffer.from(fileContent, 'utf8')
    })

    // 获取下载链接
    const downloadResult = await cloud.getTempFileURL({
      fileList: [uploadResult.fileID]
    })

    return {
      success: true,
      fileUrl: downloadResult.fileList[0].tempFileURL,
      fileName
    }
  } catch (error) {
    console.error('导出数据失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 生成CSV格式数据
function generateCSV(data) {
  const headers = [
    '检测时间',
    '样品名称',
    '采样位置',
    '铝含量(%)',
    '硅含量(%)',
    '铁含量(%)',
    '钛含量(%)',
    '水分(%)',
    '品质等级',
    '备注'
  ]

  let csv = headers.join(',') + '\n'

  data.forEach(item => {
    const row = [
      formatDate(item.createTime),
      item.sampleName || '',
      item.location || '',
      item.alContent || '',
      item.siContent || '',
      item.feContent || '',
      item.tiContent || '',
      item.moisture || '',
      item.gradeText || '',
      item.notes || ''
    ]
    csv += row.map(field => `"${field}"`).join(',') + '\n'
  })

  return csv
}

// 生成文本报告
function generateTextReport(data) {
  let report = '铝土矿检测数据报告\n'
  report += '=' * 50 + '\n\n'
  report += `生成时间: ${formatDate(new Date())}\n`
  report += `数据条数: ${data.length}\n\n`

  data.forEach((item, index) => {
    report += `检测记录 ${index + 1}:\n`
    report += `-`.repeat(30) + '\n'
    report += `检测时间: ${formatDate(item.createTime)}\n`
    report += `样品名称: ${item.sampleName || '未命名'}\n`
    report += `采样位置: ${item.location || '未知'}\n`
    report += `铝含量: ${item.alContent}%\n`
    report += `硅含量: ${item.siContent}%\n`
    report += `铁含量: ${item.feContent}%\n`
    report += `钛含量: ${item.tiContent}%\n`
    report += `水分: ${item.moisture}%\n`
    report += `品质等级: ${item.gradeText}\n`
    if (item.notes) {
      report += `备注: ${item.notes}\n`
    }
    report += '\n'
  })

  return report
}

// 格式化日期
function formatDate(date) {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
