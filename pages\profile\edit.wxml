<view class="container">
  <view class="form-section">
    <!-- 头像 -->
    <view class="form-item">
      <text class="label">头像</text>
      <view class="avatar-section" bindtap="chooseAvatar">
        <view class="avatar-container">
          <image wx:if="{{userInfo.avatar}}" class="avatar" src="{{userInfo.avatar}}" mode="aspectFill"></image>
          <view wx:else class="default-avatar">👤</view>
        </view>
        <text class="avatar-tip">点击更换头像</text>
      </view>
    </view>

    <!-- 昵称 -->
    <view class="form-item">
      <text class="label required">昵称</text>
      <input 
        class="input" 
        placeholder="请输入昵称" 
        value="{{userInfo.nickName}}"
        bindinput="onNickNameInput"
        maxlength="20"
      />
    </view>

    <!-- 手机号 -->
    <view class="form-item">
      <text class="label">手机号</text>
      <input 
        class="input" 
        placeholder="请输入手机号" 
        value="{{userInfo.phone}}"
        bindinput="onPhoneInput"
        type="number"
        maxlength="11"
      />
    </view>

    <!-- 邮箱 -->
    <view class="form-item">
      <text class="label">邮箱</text>
      <input 
        class="input" 
        placeholder="请输入邮箱地址" 
        value="{{userInfo.email}}"
        bindinput="onEmailInput"
        type="email"
      />
    </view>

    <!-- 公司 -->
    <view class="form-item">
      <text class="label">公司</text>
      <input 
        class="input" 
        placeholder="请输入公司名称" 
        value="{{userInfo.company}}"
        bindinput="onCompanyInput"
        maxlength="50"
      />
    </view>

    <!-- 职位 -->
    <view class="form-item">
      <text class="label">职位</text>
      <input 
        class="input" 
        placeholder="请输入职位" 
        value="{{userInfo.position}}"
        bindinput="onPositionInput"
        maxlength="30"
      />
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="action-btn secondary" bindtap="resetForm">重置</button>
    <button class="action-btn primary" bindtap="saveProfile" loading="{{loading}}">
      {{loading ? '保存中...' : '保存'}}
    </button>
  </view>
</view>
