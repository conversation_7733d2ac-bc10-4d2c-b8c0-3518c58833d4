<view class="container">
  <!-- 导航栏 -->
  <view class="header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <view class="header-title">系统设置</view>
  </view>

  <!-- 应用设置 -->
  <view class="section">
    <view class="section-title">应用设置</view>
    <view class="setting-list">
      <!-- 语言设置 -->
      <view class="setting-item" bindtap="setLanguage">
        <view class="item-left">
          <text class="item-icon">🌐</text>
          <view class="item-info">
            <text class="item-title">语言</text>
            <text class="item-desc">设置应用显示语言</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-value">{{currentLanguage}}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 主题设置 -->
      <view class="setting-item" bindtap="setTheme">
        <view class="item-left">
          <text class="item-icon">🎨</text>
          <view class="item-info">
            <text class="item-title">主题外观</text>
            <text class="item-desc">选择应用主题颜色</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-value">{{currentTheme}}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 字体大小 -->
      <view class="setting-item" bindtap="setFontSize">
        <view class="item-left">
          <text class="item-icon">📝</text>
          <view class="item-info">
            <text class="item-title">字体大小</text>
            <text class="item-desc">调整文字显示大小</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-value">{{currentFontSize}}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 自动保存 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">💾</text>
          <view class="item-info">
            <text class="item-title">自动保存</text>
            <text class="item-desc">自动保存检测数据</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{autoSave}}" bindchange="onAutoSaveChange" color="#2B85E4"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 检测设置 -->
  <view class="section">
    <view class="section-title">检测设置</view>
    <view class="setting-list">
      <!-- 检测精度 -->
      <view class="setting-item" bindtap="setDetectionAccuracy">
        <view class="item-left">
          <text class="item-icon">🎯</text>
          <view class="item-info">
            <text class="item-title">检测精度</text>
            <text class="item-desc">设置检测精度等级</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-value">{{detectionAccuracy}}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 检测单位 -->
      <view class="setting-item" bindtap="setDetectionUnit">
        <view class="item-left">
          <text class="item-icon">📏</text>
          <view class="item-info">
            <text class="item-title">检测单位</text>
            <text class="item-desc">设置检测结果显示单位</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-value">{{detectionUnit}}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 自动校准 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">⚖️</text>
          <view class="item-info">
            <text class="item-title">自动校准</text>
            <text class="item-desc">检测前自动校准设备</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{autoCalibration}}" bindchange="onAutoCalibrationChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 检测提醒 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">🔔</text>
          <view class="item-info">
            <text class="item-title">检测提醒</text>
            <text class="item-desc">检测完成时声音提醒</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{detectionAlert}}" bindchange="onDetectionAlertChange" color="#2B85E4"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据设置 -->
  <view class="section">
    <view class="section-title">数据设置</view>
    <view class="setting-list">
      <!-- 数据同步 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">☁️</text>
          <view class="item-info">
            <text class="item-title">云端同步</text>
            <text class="item-desc">自动同步数据到云端</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{cloudSync}}" bindchange="onCloudSyncChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 数据备份 -->
      <view class="setting-item" bindtap="backupData">
        <view class="item-left">
          <text class="item-icon">📦</text>
          <view class="item-info">
            <text class="item-title">数据备份</text>
            <text class="item-desc">备份本地检测数据</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">备份</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 数据清理 -->
      <view class="setting-item" bindtap="clearData">
        <view class="item-left">
          <text class="item-icon">🗑️</text>
          <view class="item-info">
            <text class="item-title">数据清理</text>
            <text class="item-desc">清理缓存和临时数据</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">清理</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 存储空间 -->
      <view class="setting-item" bindtap="viewStorage">
        <view class="item-left">
          <text class="item-icon">💽</text>
          <view class="item-info">
            <text class="item-title">存储空间</text>
            <text class="item-desc">已使用 {{usedStorage}}MB / {{totalStorage}}MB</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">查看</text>
          <text class="arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 通知设置 -->
  <view class="section">
    <view class="section-title">通知设置</view>
    <view class="setting-list">
      <!-- 推送通知 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📢</text>
          <view class="item-info">
            <text class="item-title">推送通知</text>
            <text class="item-desc">接收系统推送消息</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{pushNotification}}" bindchange="onPushNotificationChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 论坛通知 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">💬</text>
          <view class="item-info">
            <text class="item-title">论坛通知</text>
            <text class="item-desc">接收论坛回复和点赞通知</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{forumNotification}}" bindchange="onForumNotificationChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 系统更新 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">🔄</text>
          <view class="item-info">
            <text class="item-title">更新通知</text>
            <text class="item-desc">接收系统更新提醒</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{updateNotification}}" bindchange="onUpdateNotificationChange" color="#2B85E4"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 高级设置 -->
  <view class="section">
    <view class="section-title">高级设置</view>
    <view class="setting-list">
      <!-- 开发者模式 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">🔧</text>
          <view class="item-info">
            <text class="item-title">开发者模式</text>
            <text class="item-desc">显示调试信息和日志</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{developerMode}}" bindchange="onDeveloperModeChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 性能监控 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📊</text>
          <view class="item-info">
            <text class="item-title">性能监控</text>
            <text class="item-desc">监控应用性能数据</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{performanceMonitor}}" bindchange="onPerformanceMonitorChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 错误报告 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">🐛</text>
          <view class="item-info">
            <text class="item-title">错误报告</text>
            <text class="item-desc">自动发送错误报告</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{errorReporting}}" bindchange="onErrorReportingChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 重置设置 -->
      <view class="setting-item" bindtap="resetSettings">
        <view class="item-left">
          <text class="item-icon">🔄</text>
          <view class="item-info">
            <text class="item-title">重置设置</text>
            <text class="item-desc">恢复所有设置到默认值</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action danger">重置</text>
          <text class="arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 关于信息 -->
  <view class="section">
    <view class="section-title">关于</view>
    <view class="setting-list">
      <!-- 版本信息 -->
      <view class="setting-item" bindtap="viewVersionInfo">
        <view class="item-left">
          <text class="item-icon">ℹ️</text>
          <view class="item-info">
            <text class="item-title">版本信息</text>
            <text class="item-desc">当前版本：{{appVersion}}</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">详情</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 检查更新 -->
      <view class="setting-item" bindtap="checkUpdate">
        <view class="item-left">
          <text class="item-icon">🔄</text>
          <view class="item-info">
            <text class="item-title">检查更新</text>
            <text class="item-desc">检查是否有新版本</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">检查</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 用户协议 -->
      <view class="setting-item" bindtap="viewUserAgreement">
        <view class="item-left">
          <text class="item-icon">📄</text>
          <view class="item-info">
            <text class="item-title">用户协议</text>
            <text class="item-desc">查看用户使用协议</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">查看</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 联系我们 -->
      <view class="setting-item" bindtap="contactUs">
        <view class="item-left">
          <text class="item-icon">📞</text>
          <view class="item-info">
            <text class="item-title">联系我们</text>
            <text class="item-desc">获取技术支持和帮助</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">联系</text>
          <text class="arrow">></text>
        </view>
      </view>
    </view>
  </view>
</view>