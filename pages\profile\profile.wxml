<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card" wx:if="{{userInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
    <view class="user-info">
      <text class="username">{{userInfo.nickName}}</text>
      <text class="user-desc">注册会员</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-list">
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/history/history">
      <text>检测历史</text>
      <image src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/settings/settings">
      <text>系统设置</text>
      <image src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/about/about">
      <text>关于我们</text>
      <image src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 登出按钮 -->
  <button class="logout-btn" bindtap="logout">退出登录</button>
</view>
