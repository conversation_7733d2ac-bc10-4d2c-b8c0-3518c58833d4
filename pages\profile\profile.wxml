<view class="container">
  <!-- 用户信息头部 -->
  <view class="profile-header">
    <image class="header-bg" src="/assets/images/profile-bg.jpg" mode="aspectFill"></image>
    <view class="header-overlay">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatarUrl || '/assets/icons/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="nickname">{{userInfo.nickName || '未登录'}}</text>
          <text class="user-id">ID: {{userInfo.openid || 'Guest'}}</text>
          <view class="user-level">
            <image src="/assets/icons/vip.png" mode="aspectFit"></image>
            <text>专业用户</text>
          </view>
        </view>
      </view>

      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{totalDetections}}</text>
          <text class="stat-label">检测次数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{totalDays}}</text>
          <text class="stat-label">使用天数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{forumPosts}}</text>
          <text class="stat-label">发帖数</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷功能 -->
  <view class="quick-functions">
    <view class="function-item" bindtap="viewHistory">
      <image class="function-icon" src="/assets/icons/history-blue.png" mode="aspectFit"></image>
      <text class="function-text">检测历史</text>
    </view>
    <view class="function-item" bindtap="viewFavorites">
      <image class="function-icon" src="/assets/icons/favorite-blue.png" mode="aspectFit"></image>
      <text class="function-text">我的收藏</text>
    </view>
    <view class="function-item" bindtap="viewReports">
      <image class="function-icon" src="/assets/icons/report-blue.png" mode="aspectFit"></image>
      <text class="function-text">检测报告</text>
    </view>
    <view class="function-item" bindtap="dataExport">
      <image class="function-icon" src="/assets/icons/export-blue.png" mode="aspectFit"></image>
      <text class="function-text">数据导出</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="group-title">
        <image src="/assets/icons/user-settings.png" mode="aspectFit"></image>
        <text>账户管理</text>
      </view>

      <view class="menu-item" bindtap="editProfile">
        <view class="item-left">
          <image src="/assets/icons/edit-profile.png" mode="aspectFit"></image>
          <text>编辑资料</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>

      <view class="menu-item" bindtap="accountSecurity">
        <view class="item-left">
          <image src="/assets/icons/security.png" mode="aspectFit"></image>
          <text>账户安全</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>

      <view class="menu-item" bindtap="privacySettings">
        <view class="item-left">
          <image src="/assets/icons/privacy.png" mode="aspectFit"></image>
          <text>隐私设置</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <view class="menu-group">
      <view class="group-title">
        <image src="/assets/icons/app-settings.png" mode="aspectFit"></image>
        <text>应用设置</text>
      </view>

      <view class="menu-item" bindtap="systemSettings">
        <view class="item-left">
          <image src="/assets/icons/settings.png" mode="aspectFit"></image>
          <text>系统设置</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>

      <view class="menu-item" bindtap="deviceManagement">
        <view class="item-left">
          <image src="/assets/icons/device-manage.png" mode="aspectFit"></image>
          <text>设备管理</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>

      <view class="menu-item" bindtap="notificationSettings">
        <view class="item-left">
          <image src="/assets/icons/notification.png" mode="aspectFit"></image>
          <text>消息通知</text>
        </view>
        <view class="item-right">
          <switch checked="{{notificationEnabled}}" bindchange="toggleNotification" color="#2B85E4"></switch>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="group-title">
        <image src="/assets/icons/help-support.png" mode="aspectFit"></image>
        <text>帮助支持</text>
      </view>

      <view class="menu-item" bindtap="userGuide">
        <view class="item-left">
          <image src="/assets/icons/guide.png" mode="aspectFit"></image>
          <text>使用指南</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>

      <view class="menu-item" bindtap="feedback">
        <view class="item-left">
          <image src="/assets/icons/feedback.png" mode="aspectFit"></image>
          <text>意见反馈</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>

      <view class="menu-item" bindtap="aboutApp">
        <view class="item-left">
          <image src="/assets/icons/about.png" mode="aspectFit"></image>
          <text>关于我们</text>
        </view>
        <view class="item-right">
          <text class="version">v1.0.0</text>
        </view>
        <image class="arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo}}">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>

  <!-- 登录提示 -->
  <view class="login-prompt" wx:else>
    <image src="/assets/icons/login-prompt.png" mode="aspectFit"></image>
    <text class="prompt-text">登录后享受更多功能</text>
    <button class="login-btn" bindtap="goToLogin">立即登录</button>
  </view>
</view>
