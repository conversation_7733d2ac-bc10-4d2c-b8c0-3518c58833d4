<view class="container">
  <!-- 用户信息头部 -->
  <view class="profile-header">
    <view class="header-bg">🌌</view>
    <view class="header-overlay">
      <view class="user-info">
        <view class="avatar" wx:if="{{userInfo.avatarUrl}}">
          <image src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        </view>
        <view class="avatar default-avatar" wx:else>👤</view>
        <view class="user-details">
          <text class="nickname">{{userInfo.nickName || '未登录'}}</text>
          <text class="user-id">ID: {{userInfo.openid || 'Guest'}}</text>
          <view class="user-level">
            <text class="vip-icon">👑</text>
            <text>专业用户</text>
          </view>
        </view>
      </view>

      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{totalDetections}}</text>
          <text class="stat-label">检测次数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{totalDays}}</text>
          <text class="stat-label">使用天数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{forumPosts}}</text>
          <text class="stat-label">发帖数</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷功能 -->
  <view class="quick-functions">
    <view class="function-item" bindtap="viewHistory">
      <view class="function-icon">📊</view>
      <text class="function-text">检测历史</text>
    </view>
    <view class="function-item" bindtap="viewFavorites">
      <view class="function-icon">❤️</view>
      <text class="function-text">我的收藏</text>
    </view>
    <view class="function-item" bindtap="viewReports">
      <view class="function-icon">📋</view>
      <text class="function-text">检测报告</text>
    </view>
    <view class="function-item" bindtap="dataExport">
      <view class="function-icon">📤</view>
      <text class="function-text">数据导出</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="group-title">
        <view class="group-icon">👤</view>
        <text>账户管理</text>
      </view>

      <view class="menu-item" bindtap="editProfile">
        <view class="item-left">
          <view class="menu-icon">✏️</view>
          <text>编辑资料</text>
        </view>
        <view class="arrow">▶</view>
      </view>

      <view class="menu-item" bindtap="accountSecurity">
        <view class="item-left">
          <view class="menu-icon">🔒</view>
          <text>账户安全</text>
        </view>
        <view class="arrow">▶</view>
      </view>

      <view class="menu-item" bindtap="privacySettings">
        <view class="item-left">
          <view class="menu-icon">🛡️</view>
          <text>隐私设置</text>
        </view>
        <view class="arrow">▶</view>
      </view>
    </view>

    <view class="menu-group">
      <view class="group-title">
        <view class="group-icon">⚙️</view>
        <text>应用设置</text>
      </view>

      <view class="menu-item" bindtap="systemSettings">
        <view class="item-left">
          <view class="menu-icon">🔧</view>
          <text>系统设置</text>
        </view>
        <view class="arrow">▶</view>
      </view>

      <view class="menu-item" bindtap="deviceManagement">
        <view class="item-left">
          <view class="menu-icon">📱</view>
          <text>设备管理</text>
        </view>
        <view class="arrow">▶</view>
      </view>

      <view class="menu-item" bindtap="notificationSettings">
        <view class="item-left">
          <view class="menu-icon">🔔</view>
          <text>消息通知</text>
        </view>
        <view class="item-right">
          <switch checked="{{notificationEnabled}}" bindchange="toggleNotification" color="#2B85E4"></switch>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="group-title">
        <view class="group-icon">❓</view>
        <text>帮助支持</text>
      </view>

      <view class="menu-item" bindtap="userGuide">
        <view class="item-left">
          <view class="menu-icon">📖</view>
          <text>使用指南</text>
        </view>
        <view class="arrow">▶</view>
      </view>

      <view class="menu-item" bindtap="feedback">
        <view class="item-left">
          <view class="menu-icon">💬</view>
          <text>意见反馈</text>
        </view>
        <view class="arrow">▶</view>
      </view>

      <view class="menu-item" bindtap="aboutApp">
        <view class="item-left">
          <view class="menu-icon">ℹ️</view>
          <text>关于我们</text>
        </view>
        <view class="item-right">
          <text class="version">v1.0.0</text>
        </view>
        <view class="arrow">▶</view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo}}">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>

  <!-- 登录提示 -->
  <view class="login-prompt" wx:else>
    <view class="login-icon">🔐</view>
    <text class="prompt-text">登录后享受更多功能</text>
    <button class="login-btn" bindtap="goToLogin">立即登录</button>
  </view>
</view>
