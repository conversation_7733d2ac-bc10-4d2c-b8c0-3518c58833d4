/* pages/post/create.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  color: #666;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 32rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-right .publish-btn {
  font-size: 32rpx;
  color: #999;
  transition: color 0.3s;
}

.header-right .publish-btn.active {
  color: #2B85E4;
}

/* 发帖表单 */
.post-form {
  padding: 0 30rpx;
}

.form-section {
  background-color: #fff;
  margin: 20rpx 0;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 分类选择 */
.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  min-width: 120rpx;
  transition: all 0.3s;
}

.category-item.selected {
  border-color: #2B85E4;
  background-color: #f0f8ff;
}

.category-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 24rpx;
  color: #666;
}

.category-item.selected .category-name {
  color: #2B85E4;
}

/* 标题输入 */
.title-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: #fafafa;
}

.title-input:focus {
  border-color: #2B85E4;
  background-color: #fff;
}

.input-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 内容输入 */
.content-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 30rpx;
  line-height: 1.6;
  background-color: #fafafa;
}

.content-textarea:focus {
  border-color: #2B85E4;
  background-color: #fff;
}

/* 图片上传 */
.image-upload {
  margin-top: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  object-fit: cover;
}

.delete-image {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.add-image-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ccc;
  border-radius: 8rpx;
  color: #999;
  transition: all 0.3s;
}

.add-image-btn:active {
  border-color: #2B85E4;
  color: #2B85E4;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.add-text {
  font-size: 24rpx;
}

.image-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  text-align: center;
}

/* 标签相关 */
.tag-input-area {
  display: flex;
  gap: 20rpx;
  align-items: center;
  margin-bottom: 20rpx;
}

.tag-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 30rpx;
  background-color: #fafafa;
}

.tag-input:focus {
  border-color: #2B85E4;
  background-color: #fff;
}

.add-tag-btn {
  padding: 20rpx 30rpx;
  background-color: #2B85E4;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.add-tag-btn[disabled] {
  background-color: #ccc;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: #f0f8ff;
  border: 1rpx solid #2B85E4;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
}

.tag-text {
  font-size: 26rpx;
  color: #2B85E4;
  margin-right: 10rpx;
}

.remove-tag {
  font-size: 24rpx;
  color: #999;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0,0,0,0.1);
}

.tag-suggestions {
  margin-top: 20rpx;
}

.suggestion-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.suggestion-tag {
  display: inline-block;
  margin: 0 15rpx 15rpx 0;
  padding: 8rpx 16rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 16rpx;
  font-size: 24rpx;
  transition: all 0.3s;
}

.suggestion-tag:active {
  background-color: #2B85E4;
  color: #fff;
}

/* 发布设置 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 30rpx;
  color: #333;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.cancel-btn {
  flex: 1;
  padding: 24rpx;
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
}

.publish-btn-main {
  flex: 2;
  padding: 24rpx;
  background-color: #ccc;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: background-color 0.3s;
}

.publish-btn-main.active {
  background-color: #2B85E4;
}

.publish-btn-main[disabled] {
  background-color: #ccc;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .category-list {
    justify-content: space-between;
  }

  .category-item {
    min-width: 100rpx;
    flex: 1;
  }

  .image-item,
  .add-image-btn {
    width: 160rpx;
    height: 160rpx;
  }
}

/* 动画效果 */
.form-section {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.publishing {
  opacity: 0.7;
  pointer-events: none;
}