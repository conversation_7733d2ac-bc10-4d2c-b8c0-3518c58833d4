# 导航栏图标说明

由于微信小程序的tabBar需要真实的PNG图片文件，这里提供了图标的设计规范和建议。

## 图标规范

### 尺寸要求
- **普通屏幕**: 78x78px
- **高清屏幕**: 156x156px (建议同时提供)
- **格式**: PNG格式，支持透明背景

### 颜色规范
- **未选中状态**: #7A7E83 (灰色)
- **选中状态**: #2B85E4 (蓝色)

### 设计风格
- **风格**: 线性图标，简洁现代
- **线条粗细**: 2-3px
- **圆角**: 适度圆角，保持一致性
- **填充**: 选中状态可以使用填充效果

## 图标内容建议

### 1. 首页图标 (home.png / home-active.png)
```
未选中: 房子轮廓线条图标
选中: 房子填充图标
建议元素: 简单的房屋形状，可包含门和窗户轮廓
```

### 2. 设备图标 (device.png / device-active.png)
```
未选中: 手机/平板设备轮廓图标
选中: 设备填充图标
建议元素: 矩形设备外框，可包含屏幕和按钮
```

### 3. 论坛图标 (forum.png / forum-active.png)
```
未选中: 对话气泡轮廓图标
选中: 对话气泡填充图标
建议元素: 圆角矩形气泡，可包含三个点或文字线条
```

### 4. 个人中心图标 (profile.png / profile-active.png)
```
未选中: 用户头像轮廓图标
选中: 用户头像填充图标
建议元素: 圆形头部和肩膀轮廓
```

## 临时解决方案

在没有真实PNG图标的情况下，可以考虑以下方案：

### 方案1: 使用在线图标生成器
- Iconfont (阿里巴巴矢量图标库)
- Feather Icons
- Heroicons
- Material Design Icons

### 方案2: 使用设计工具创建
- Figma (免费)
- Sketch
- Adobe Illustrator
- Canva

### 方案3: 使用字体图标
虽然tabBar不支持字体图标，但可以将字体图标转换为PNG：
- 🏠 (首页)
- 📱 (设备)
- 💬 (论坛)
- 👤 (个人)

## 图标生成步骤

1. **设计图标**: 使用设计工具创建SVG格式的图标
2. **导出PNG**: 按照规定尺寸导出PNG文件
3. **优化文件**: 压缩图片大小，保持清晰度
4. **测试效果**: 在不同设备上测试显示效果

## 注意事项

1. **版权问题**: 确保使用的图标有合法授权
2. **一致性**: 保持所有图标的设计风格一致
3. **可读性**: 在小尺寸下仍能清晰识别
4. **适配性**: 在不同屏幕密度下都能正常显示

## 当前状态

目前的图标文件是占位符，需要替换为真实的PNG图标文件。建议优先级：

1. ✅ 配置完成 - tabBar配置已添加
2. 🔄 待完成 - 创建真实的PNG图标文件
3. 🔄 待完成 - 测试在不同设备上的显示效果
4. 🔄 待完成 - 优化图标文件大小
