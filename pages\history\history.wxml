<view class="container">
  <!-- 顶部标签栏 -->
  <view class="tab-bar">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}"
      wx:for="{{tabs}}"
      wx:key="index"
      bindtap="switchTab"
      data-index="{{index}}"
    >
      {{item}}
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar">
    <view class="total-count">
      共 {{historyList.length}} 条记录
    </view>
    <button class="export-btn" bindtap="exportData">
      <image src="/assets/icons/export.png" mode="aspectFit"></image>
      导出
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <image class="loading-icon" src="/assets/icons/loading.gif" mode="aspectFit"></image>
    <text>加载中...</text>
  </view>

  <!-- 历史记录列表 -->
  <view class="history-list" wx:else>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{historyList.length === 0}}">
      <image class="empty-icon" src="/assets/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无检测记录</text>
      <button class="start-detection-btn" bindtap="startDetection">开始检测</button>
    </view>

    <!-- 记录列表 -->
    <view class="history-item" wx:for="{{historyList}}" wx:key="_id">
      <view class="item-header">
        <view class="date-time">
          <text class="date">{{item.date}}</text>
          <text class="time">{{item.time}}</text>
        </view>
        <view class="actions">
          <button class="action-btn" bindtap="viewDetail" data-item="{{item}}">
            <image src="/assets/icons/view.png" mode="aspectFit"></image>
          </button>
          <button class="action-btn delete" bindtap="deleteRecord" data-item="{{item}}">
            <image src="/assets/icons/delete.png" mode="aspectFit"></image>
          </button>
        </view>
      </view>

      <view class="item-content">
        <view class="detection-data">
          <view class="data-item">
            <text class="label">铝含量</text>
            <text class="value">{{item.alContent}}%</text>
          </view>
          <view class="data-item">
            <text class="label">硅含量</text>
            <text class="value">{{item.siContent}}%</text>
          </view>
          <view class="data-item">
            <text class="label">铁含量</text>
            <text class="value">{{item.feContent}}%</text>
          </view>
        </view>

        <view class="sample-info" wx:if="{{item.sampleName}}">
          <text class="sample-name">样品：{{item.sampleName}}</text>
          <text class="location" wx:if="{{item.location}}">位置：{{item.location}}</text>
        </view>

        <view class="quality-grade">
          <text class="grade-label">品质等级：</text>
          <text class="grade-value {{item.grade}}">{{item.gradeText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
