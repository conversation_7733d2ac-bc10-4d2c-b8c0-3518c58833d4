<view class="container">
  <!-- 导航栏 -->
  <view class="header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <view class="header-title">发布帖子</view>
    <view class="header-right" bindtap="publishPost">
      <text class="publish-btn {{canPublish ? 'active' : ''}}">发布</text>
    </view>
  </view>

  <!-- 发帖表单 -->
  <view class="post-form">
    <!-- 帖子分类 -->
    <view class="form-section">
      <view class="section-title">选择分类</view>
      <view class="category-list">
        <view
          class="category-item {{selectedCategory === item.id ? 'selected' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          bindtap="selectCategory"
          data-id="{{item.id}}"
        >
          <text class="category-icon">{{item.icon}}</text>
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 帖子标题 -->
    <view class="form-section">
      <view class="section-title">帖子标题</view>
      <input
        class="title-input"
        placeholder="请输入帖子标题（必填）"
        value="{{title}}"
        bindinput="onTitleInput"
        maxlength="50"
      />
      <view class="input-counter">{{title.length}}/50</view>
    </view>

    <!-- 帖子内容 -->
    <view class="form-section">
      <view class="section-title">帖子内容</view>
      <textarea
        class="content-textarea"
        placeholder="分享您的经验、问题或见解..."
        value="{{content}}"
        bindinput="onContentInput"
        maxlength="2000"
        auto-height
      />
      <view class="input-counter">{{content.length}}/2000</view>
    </view>

    <!-- 图片上传 -->
    <view class="form-section">
      <view class="section-title">添加图片（可选）</view>
      <view class="image-upload">
        <view class="image-list">
          <view
            class="image-item"
            wx:for="{{images}}"
            wx:key="index"
          >
            <image src="{{item}}" mode="aspectFill" class="uploaded-image"/>
            <view class="delete-image" bindtap="deleteImage" data-index="{{index}}">×</view>
          </view>
          <view
            class="add-image-btn"
            wx:if="{{images.length < 9}}"
            bindtap="chooseImage"
          >
            <text class="add-icon">+</text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        <view class="image-tip">最多可上传9张图片</view>
      </view>
    </view>

    <!-- 标签 -->
    <view class="form-section">
      <view class="section-title">添加标签（可选）</view>
      <view class="tag-input-area">
        <input
          class="tag-input"
          placeholder="输入标签后按回车添加"
          value="{{currentTag}}"
          bindinput="onTagInput"
          bindconfirm="addTag"
        />
        <button class="add-tag-btn" bindtap="addTag" disabled="{{!currentTag}}">添加</button>
      </view>
      <view class="tag-list" wx:if="{{tags.length > 0}}">
        <view
          class="tag-item"
          wx:for="{{tags}}"
          wx:key="index"
        >
          <text class="tag-text">#{{item}}</text>
          <text class="remove-tag" bindtap="removeTag" data-index="{{index}}">×</text>
        </view>
      </view>
      <view class="tag-suggestions">
        <text class="suggestion-title">推荐标签：</text>
        <view
          class="suggestion-tag"
          wx:for="{{suggestedTags}}"
          wx:key="index"
          bindtap="addSuggestedTag"
          data-tag="{{item}}"
        >
          #{{item}}
        </view>
      </view>
    </view>

    <!-- 发布设置 -->
    <view class="form-section">
      <view class="section-title">发布设置</view>
      <view class="setting-item">
        <text class="setting-label">匿名发布</text>
        <switch checked="{{isAnonymous}}" bindchange="toggleAnonymous" color="#2B85E4"/>
      </view>
      <view class="setting-item">
        <text class="setting-label">允许评论</text>
        <switch checked="{{allowComments}}" bindchange="toggleComments" color="#2B85E4"/>
      </view>
    </view>
  </view>

  <!-- 发布按钮 -->
  <view class="bottom-actions">
    <button class="cancel-btn" bindtap="goBack">取消</button>
    <button
      class="publish-btn-main {{canPublish ? 'active' : ''}}"
      bindtap="publishPost"
      disabled="{{!canPublish || publishing}}"
    >
      {{publishing ? '发布中...' : '发布帖子'}}
    </button>
  </view>
</view>