.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.info-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.device-icon {
  font-size: 60rpx;
}

.device-basic {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.device-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.device-model {
  font-size: 26rpx;
  color: #666;
}

.connection-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.connection-status.connected {
  background-color: #d4edda;
  color: #155724;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 24rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.battery-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.battery-bar {
  width: 80rpx;
  height: 20rpx;
  background-color: #e9ecef;
  border-radius: 10rpx;
  overflow: hidden;
}

.battery-fill {
  height: 100%;
  background-color: #28a745;
  transition: width 0.3s;
}

.battery-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #28a745;
}

.stats-section, .specs-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #2B85E4;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2B85E4;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.specs-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-label {
  font-size: 28rpx;
  color: #333;
}

.spec-value {
  font-size: 26rpx;
  color: #666;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background-color: #2B85E4;
  color: white;
}

.action-btn.secondary {
  background-color: #28a745;
  color: white;
}

.action-btn.tertiary {
  background-color: #f8f9fa;
  color: #333;
  border: 1rpx solid #e9ecef;
}

.action-btn.danger {
  background-color: #ff4757;
  color: white;
}

.btn-icon {
  font-size: 32rpx;
}
