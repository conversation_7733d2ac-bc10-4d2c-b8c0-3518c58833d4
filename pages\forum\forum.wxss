.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 论坛头部 */
.forum-header {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: white;
  border-radius: 25rpx;
  font-size: 28rpx;
  border: 1rpx solid #e0e0e0;
}

.create-btn {
  display: flex;
  align-items: center;
  padding: 0 25rpx;
  background-color: #2B85E4;
  color: white;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
  white-space: nowrap;
}

.create-btn image {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

/* 分类标签 */
.category-tabs {
  margin-bottom: 20rpx;
}

.tab-scroll {
  white-space: nowrap;
}

.tab-list {
  display: inline-flex;
  gap: 15rpx;
  padding: 0 5rpx;
}

.tab-item {
  padding: 15rpx 25rpx;
  background-color: white;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  border: 1rpx solid #e0e0e0;
}

.tab-item.active {
  background-color: #2B85E4;
  color: white;
  border-color: #2B85E4;
}

/* 排序选项 */
.sort-options {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.sort-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.sort-item.active {
  background-color: #e3f2fd;
  color: #2B85E4;
  border-color: #2B85E4;
}

.sort-item image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

/* 帖子列表 */
.post-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  background-color: white;
  border-radius: 16rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.create-first-btn {
  background-color: #2B85E4;
  color: white;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  border: none;
}

.post-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 帖子头部 */
.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.author-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.post-time {
  font-size: 22rpx;
  color: #999;
}

.category-tag {
  padding: 8rpx 15rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}

.category-tag.tech {
  background-color: #4caf50;
}

.category-tag.experience {
  background-color: #ff9800;
}

.category-tag.help {
  background-color: #f44336;
}

.category-tag.device {
  background-color: #9c27b0;
}

.category-tag.news {
  background-color: #2196f3;
}

/* 帖子内容 */
.post-content {
  margin-bottom: 15rpx;
}

.post-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
  line-height: 1.4;
}

.post-excerpt {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 图片预览 */
.image-preview {
  display: flex;
  gap: 10rpx;
  margin-top: 15rpx;
  overflow-x: auto;
}

.preview-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

/* 帖子统计 */
.post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.stat-item image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

/* 加载更多 */
.load-more {
  padding: 30rpx 0;
  text-align: center;
}

.load-more-btn {
  background-color: #f5f5f5;
  color: #666;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  font-size: 26rpx;
  border: none;
}

.no-more {
  padding: 30rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}
