Component({
  properties: {
    // 图表类型：bar, pie, line
    type: {
      type: String,
      value: 'bar'
    },
    // 图表数据
    data: {
      type: Array,
      value: []
    },
    // 图表配置
    options: {
      type: Object,
      value: {}
    },
    // 图表宽度
    width: {
      type: Number,
      value: 300
    },
    // 图表高度
    height: {
      type: Number,
      value: 200
    }
  },

  data: {
    canvasId: '',
    ctx: null
  },

  lifetimes: {
    attached() {
      // 生成唯一的canvas ID
      this.setData({
        canvasId: 'chart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      })
    },

    ready() {
      this.initChart()
    }
  },

  observers: {
    'data, type, options': function() {
      if (this.data.ctx) {
        this.drawChart()
      }
    }
  },

  methods: {
    // 初始化图表
    initChart() {
      const query = this.createSelectorQuery()
      query.select('#' + this.data.canvasId)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0]) {
            const canvas = res[0].node
            const ctx = canvas.getContext('2d')
            
            // 设置canvas尺寸
            const dpr = wx.getSystemInfoSync().pixelRatio
            canvas.width = this.data.width * dpr
            canvas.height = this.data.height * dpr
            ctx.scale(dpr, dpr)

            this.setData({ ctx })
            this.drawChart()
          }
        })
    },

    // 绘制图表
    drawChart() {
      const { ctx, type, data, options, width, height } = this.data
      if (!ctx || !data.length) return

      // 清空画布
      ctx.clearRect(0, 0, width, height)

      // 根据图表类型绘制
      switch (type) {
        case 'bar':
          this.drawBarChart(ctx, data, options, width, height)
          break
        case 'pie':
          this.drawPieChart(ctx, data, options, width, height)
          break
        case 'line':
          this.drawLineChart(ctx, data, options, width, height)
          break
      }
    },

    // 绘制柱状图
    drawBarChart(ctx, data, options, width, height) {
      const padding = 40
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const barWidth = chartWidth / data.length * 0.6
      const barSpacing = chartWidth / data.length * 0.4

      // 找到最大值用于缩放
      const maxValue = Math.max(...data.map(item => item.value))
      
      // 绘制坐标轴
      ctx.strokeStyle = '#e0e0e0'
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()

      // 绘制柱子
      data.forEach((item, index) => {
        const x = padding + index * (barWidth + barSpacing) + barSpacing / 2
        const barHeight = (item.value / maxValue) * chartHeight
        const y = height - padding - barHeight

        // 绘制柱子
        ctx.fillStyle = item.color || '#2B85E4'
        ctx.fillRect(x, y, barWidth, barHeight)

        // 绘制标签
        ctx.fillStyle = '#333'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(item.label, x + barWidth / 2, height - padding + 20)
        
        // 绘制数值
        ctx.fillText(item.value + '%', x + barWidth / 2, y - 10)
      })
    },

    // 绘制饼图
    drawPieChart(ctx, data, options, width, height) {
      const centerX = width / 2
      const centerY = height / 2
      const radius = Math.min(width, height) / 2 - 40

      const total = data.reduce((sum, item) => sum + item.value, 0)
      let currentAngle = -Math.PI / 2

      // 绘制饼图扇形
      data.forEach((item, index) => {
        const sliceAngle = (item.value / total) * 2 * Math.PI
        
        ctx.beginPath()
        ctx.moveTo(centerX, centerY)
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
        ctx.closePath()
        
        ctx.fillStyle = item.color || this.getDefaultColor(index)
        ctx.fill()
        
        // 绘制标签
        const labelAngle = currentAngle + sliceAngle / 2
        const labelX = centerX + Math.cos(labelAngle) * (radius + 20)
        const labelY = centerY + Math.sin(labelAngle) * (radius + 20)
        
        ctx.fillStyle = '#333'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(item.label, labelX, labelY)
        ctx.fillText(item.value + '%', labelX, labelY + 15)
        
        currentAngle += sliceAngle
      })
    },

    // 绘制折线图
    drawLineChart(ctx, data, options, width, height) {
      const padding = 40
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      const maxValue = Math.max(...data.map(item => item.value))
      const stepX = chartWidth / (data.length - 1)

      // 绘制坐标轴
      ctx.strokeStyle = '#e0e0e0'
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()

      // 绘制折线
      ctx.strokeStyle = '#2B85E4'
      ctx.lineWidth = 2
      ctx.beginPath()

      data.forEach((item, index) => {
        const x = padding + index * stepX
        const y = height - padding - (item.value / maxValue) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }

        // 绘制数据点
        ctx.fillStyle = '#2B85E4'
        ctx.beginPath()
        ctx.arc(x, y, 4, 0, 2 * Math.PI)
        ctx.fill()

        // 绘制标签
        ctx.fillStyle = '#333'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(item.label, x, height - padding + 20)
        ctx.fillText(item.value + '%', x, y - 15)
      })

      ctx.stroke()
    },

    // 获取默认颜色
    getDefaultColor(index) {
      const colors = [
        '#2B85E4', '#4CAF50', '#FF9800', '#F44336', 
        '#9C27B0', '#00BCD4', '#FFEB3B', '#795548'
      ]
      return colors[index % colors.length]
    }
  }
})
