// pages/security/security.js
const app = getApp()

Page({
  data: {
    // 安全评分
    securityScore: 85,
    securityLevel: '安全',
    securityTip: '您的账户安全性良好，建议开启两步验证',
    
    // 账户信息
    phoneNumber: '',
    email: '',
    passwordStatus: '已设置，建议定期更换',
    phoneStatus: '未绑定',
    emailStatus: '未绑定',
    wechatStatus: '已绑定微信账号',
    
    // 安全设置
    twoFactorEnabled: false,
    
    // 安全建议
    suggestions: [
      { icon: '🔒', text: '使用强密码，包含字母、数字和特殊字符' },
      { icon: '📱', text: '绑定手机号，便于找回密码和接收安全通知' },
      { icon: '🛡️', text: '开启两步验证，提高账户安全性' },
      { icon: '👀', text: '定期检查登录记录，发现异常及时处理' }
    ],
    
    // 弹窗状态
    showPasswordModal: false,
    showPhoneModal: false,
    
    // 修改密码
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    canChangePassword: false,
    
    // 绑定手机
    inputPhone: '',
    phoneCode: '',
    canSendPhoneCode: false,
    canBindPhone: false,
    phoneCodeCountdown: 0
  },

  onLoad() {
    this.loadSecurityInfo()
  },

  // 加载安全信息
  loadSecurityInfo() {
    const userInfo = app.globalData.userInfo
    const isGuest = app.globalData.isGuest
    
    if (isGuest) {
      this.setData({
        securityScore: 30,
        securityLevel: '较低',
        securityTip: '游客模式安全性较低，建议注册账号',
        passwordStatus: '游客模式无密码',
        phoneStatus: '未绑定',
        emailStatus: '未绑定',
        wechatStatus: '未绑定'
      })
    } else {
      // 从本地存储获取安全信息
      const securityInfo = wx.getStorageSync('security_info') || {}
      
      this.setData({
        phoneNumber: securityInfo.phone || '',
        email: securityInfo.email || '',
        phoneStatus: securityInfo.phone ? `已绑定 ${this.maskPhone(securityInfo.phone)}` : '未绑定',
        emailStatus: securityInfo.email ? `已绑定 ${this.maskEmail(securityInfo.email)}` : '未绑定',
        twoFactorEnabled: securityInfo.twoFactorEnabled || false
      })
      
      this.calculateSecurityScore()
    }
  },

  // 计算安全评分
  calculateSecurityScore() {
    let score = 40 // 基础分
    
    if (this.data.phoneNumber) score += 20
    if (this.data.email) score += 20
    if (this.data.twoFactorEnabled) score += 20
    
    let level = '较低'
    let tip = '建议完善安全设置'
    
    if (score >= 80) {
      level = '很安全'
      tip = '您的账户安全性很好'
    } else if (score >= 60) {
      level = '安全'
      tip = '您的账户安全性良好'
    } else if (score >= 40) {
      level = '一般'
      tip = '建议绑定手机号和邮箱'
    }
    
    this.setData({
      securityScore: score,
      securityLevel: level,
      securityTip: tip
    })
  },

  // 手机号脱敏
  maskPhone(phone) {
    if (!phone || phone.length < 11) return phone
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  },

  // 邮箱脱敏
  maskEmail(email) {
    if (!email) return email
    const [username, domain] = email.split('@')
    if (username.length <= 2) return email
    const maskedUsername = username.charAt(0) + '***' + username.slice(-1)
    return maskedUsername + '@' + domain
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 修改密码
  changePassword() {
    this.setData({
      showPasswordModal: true,
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      canChangePassword: false
    })
  },

  // 绑定手机号
  bindPhone() {
    this.setData({
      showPhoneModal: true,
      inputPhone: '',
      phoneCode: '',
      canSendPhoneCode: false,
      canBindPhone: false,
      phoneCodeCountdown: 0
    })
  },

  // 绑定邮箱
  bindEmail() {
    wx.showModal({
      title: '绑定邮箱',
      content: '邮箱绑定功能开发中，敬请期待',
      showCancel: false
    })
  },

  // 切换两步验证
  toggleTwoFactor() {
    // 在实际应用中，这里应该有验证流程
  },

  // 两步验证开关变化
  onTwoFactorChange(e) {
    const enabled = e.detail.value
    
    if (enabled && !this.data.phoneNumber) {
      wx.showModal({
        title: '需要绑定手机号',
        content: '开启两步验证需要先绑定手机号，是否立即绑定？',
        success: (res) => {
          if (res.confirm) {
            this.bindPhone()
          }
        }
      })
      return
    }
    
    this.setData({ twoFactorEnabled: enabled })
    
    // 保存到本地存储
    const securityInfo = wx.getStorageSync('security_info') || {}
    securityInfo.twoFactorEnabled = enabled
    wx.setStorageSync('security_info', securityInfo)
    
    this.calculateSecurityScore()
    
    wx.showToast({
      title: enabled ? '已开启两步验证' : '已关闭两步验证',
      icon: 'success'
    })
  },

  // 管理登录设备
  manageDevices() {
    wx.showModal({
      title: '登录设备管理',
      content: '当前设备：iPhone 13\n上次登录：刚刚\n\n设备管理功能开发中',
      showCancel: false
    })
  },

  // 查看登录记录
  viewLoginHistory() {
    wx.showModal({
      title: '登录记录',
      content: '今天 15:30 - iPhone 13\n昨天 09:15 - Windows PC\n\n登录记录功能开发中',
      showCancel: false
    })
  },

  // 退出所有设备
  logoutAllDevices() {
    wx.showModal({
      title: '退出所有设备',
      content: '确定要退出所有已登录的设备吗？您需要重新登录。',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已退出所有设备',
            icon: 'success'
          })
        }
      }
    })
  },

  // 冻结账户
  freezeAccount() {
    wx.showModal({
      title: '冻结账户',
      content: '冻结账户后将无法登录，需要联系客服解冻。确定要冻结账户吗？',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '账户冻结',
            content: '账户冻结功能开发中，如需帮助请联系客服',
            showCancel: false
          })
        }
      }
    })
  },

  // 隐藏密码弹窗
  hidePasswordModal() {
    this.setData({ showPasswordModal: false })
  },

  // 隐藏手机弹窗
  hidePhoneModal() {
    this.setData({ showPhoneModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 密码输入
  onCurrentPasswordInput(e) {
    this.setData({ currentPassword: e.detail.value })
    this.checkPasswordForm()
  },

  onNewPasswordInput(e) {
    this.setData({ newPassword: e.detail.value })
    this.checkPasswordForm()
  },

  onConfirmPasswordInput(e) {
    this.setData({ confirmPassword: e.detail.value })
    this.checkPasswordForm()
  },

  // 检查密码表单
  checkPasswordForm() {
    const { currentPassword, newPassword, confirmPassword } = this.data
    const canChange = currentPassword.length >= 6 && 
                     newPassword.length >= 6 && 
                     newPassword === confirmPassword &&
                     newPassword !== currentPassword
    
    this.setData({ canChangePassword: canChange })
  },

  // 确认修改密码
  confirmChangePassword() {
    if (!this.data.canChangePassword) return
    
    wx.showLoading({ title: '修改中...' })
    
    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '密码修改成功',
        icon: 'success'
      })
      this.hidePasswordModal()
    }, 1500)
  },

  // 手机号输入
  onPhoneInput(e) {
    const phone = e.detail.value
    this.setData({ 
      inputPhone: phone,
      canSendPhoneCode: /^1[3-9]\d{9}$/.test(phone)
    })
    this.checkPhoneForm()
  },

  // 验证码输入
  onPhoneCodeInput(e) {
    this.setData({ phoneCode: e.detail.value })
    this.checkPhoneForm()
  },

  // 检查手机表单
  checkPhoneForm() {
    const { inputPhone, phoneCode } = this.data
    const canBind = /^1[3-9]\d{9}$/.test(inputPhone) && phoneCode.length === 6
    this.setData({ canBindPhone: canBind })
  },

  // 发送验证码
  sendPhoneCode() {
    if (!this.data.canSendPhoneCode) return
    
    wx.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
    
    // 开始倒计时
    let countdown = 60
    this.setData({ phoneCodeCountdown: countdown })
    
    const timer = setInterval(() => {
      countdown--
      this.setData({ phoneCodeCountdown: countdown })
      
      if (countdown <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  },

  // 确认绑定手机
  confirmBindPhone() {
    if (!this.data.canBindPhone) return
    
    wx.showLoading({ title: '绑定中...' })
    
    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading()
      
      const phone = this.data.inputPhone
      const securityInfo = wx.getStorageSync('security_info') || {}
      securityInfo.phone = phone
      wx.setStorageSync('security_info', securityInfo)
      
      this.setData({
        phoneNumber: phone,
        phoneStatus: `已绑定 ${this.maskPhone(phone)}`
      })
      
      this.calculateSecurityScore()
      this.hidePhoneModal()
      
      wx.showToast({
        title: '手机号绑定成功',
        icon: 'success'
      })
    }, 1500)
  }
})
