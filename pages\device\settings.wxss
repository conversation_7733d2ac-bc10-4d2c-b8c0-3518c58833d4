.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.settings-section {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.setting-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.setting-desc {
  font-size: 24rpx;
  color: #666;
}

.picker-value {
  padding: 15rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #e9ecef;
  min-width: 150rpx;
  text-align: center;
}

.reset-section {
  padding: 40rpx 20rpx;
}

.reset-btn {
  width: 100%;
  padding: 30rpx;
  background-color: #ff4757;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
}
