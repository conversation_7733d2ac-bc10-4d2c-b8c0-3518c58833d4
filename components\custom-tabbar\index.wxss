.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  border-top: 1rpx solid #e5e5e5;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10rpx 0;
  position: relative;
}

.tab-bar-item-active {
  background: linear-gradient(135deg, rgba(43, 133, 228, 0.05) 0%, rgba(43, 133, 228, 0.1) 100%);
}

.tab-bar-item-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #2B85E4 0%, #1976d2 100%);
  border-radius: 2rpx;
}

.tab-icon {
  margin-bottom: 8rpx;
  position: relative;
}

.icon-text {
  font-size: 44rpx;
  transition: all 0.3s ease;
  display: block;
  transform: scale(0.9);
}

.icon-active {
  transform: scale(1.1);
  filter: drop-shadow(0 2rpx 4rpx rgba(43, 133, 228, 0.3));
}

.tab-text {
  font-size: 20rpx;
  color: #7A7E83;
  transition: all 0.3s ease;
  font-weight: 400;
}

.text-active {
  color: #2B85E4;
  font-weight: 600;
  transform: scale(1.05);
}

/* 添加点击效果 */
.tab-bar-item:active {
  background: rgba(43, 133, 228, 0.1);
  transform: scale(0.98);
}

/* 适配不同屏幕 */
@media (max-width: 320px) {
  .icon-text {
    font-size: 38rpx;
  }
  .tab-text {
    font-size: 18rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .tab-bar {
    background: #1a1a1a;
    border-top-color: #333;
  }
  
  .tab-text {
    color: #999;
  }
  
  .text-active {
    color: #2B85E4;
  }
}
