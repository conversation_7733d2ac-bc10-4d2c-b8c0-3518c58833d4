const app = getApp()

Page({
  data: {
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    loading: false
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.userInfo) {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  // 微信授权登录
  getUserProfile() {
    this.setData({ loading: true })
    
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        console.log('获取用户信息成功', res)
        
        // 保存用户信息到全局数据和本地存储
        app.globalData.userInfo = res.userInfo
        wx.setStorageSync('userInfo', res.userInfo)
        
        // 调用云函数进行登录
        this.cloudLogin(res.userInfo)
      },
      fail: (err) => {
        console.error('获取用户信息失败', err)
        wx.showToast({
          title: '授权失败',
          icon: 'error'
        })
        this.setData({ loading: false })
      }
    })
  },

  // 云开发登录
  cloudLogin(userInfo) {
    wx.cloud.callFunction({
      name: 'login',
      data: {
        userInfo: userInfo
      },
      success: (res) => {
        console.log('云函数登录成功', res)
        
        // 保存用户ID
        if (res.result && res.result.openid) {
          app.globalData.openid = res.result.openid
          wx.setStorageSync('openid', res.result.openid)
        }
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          })
        }, 1500)
      },
      fail: (err) => {
        console.error('云函数登录失败', err)
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        })
      },
      complete: () => {
        this.setData({ loading: false })
      }
    })
  },

  // 游客模式
  guestMode() {
    wx.showModal({
      title: '游客模式',
      content: '游客模式下部分功能将受限，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          // 设置游客标识
          app.globalData.isGuest = true
          wx.setStorageSync('isGuest', true)
          
          wx.switchTab({
            url: '/pages/home/<USER>'
          })
        }
      }
    })
  }
})
