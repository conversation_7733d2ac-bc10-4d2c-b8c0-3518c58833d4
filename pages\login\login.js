const app = getApp()

Page({
  data: {
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    loading: false
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.userInfo) {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  // 微信授权登录
  getUserProfile() {
    this.setData({ loading: true })

    // 检查是否在开发环境
    const systemInfo = wx.getSystemInfoSync()
    const isDevelopment = systemInfo.platform === 'devtools'

    if (isDevelopment) {
      console.log('开发环境检测到，使用模拟登录')
      this.simulateLogin()
      return
    }

    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        console.log('获取用户信息成功', res)

        // 保存用户信息到全局数据和本地存储
        app.globalData.userInfo = res.userInfo
        wx.setStorageSync('userInfo', res.userInfo)

        // 调用云函数进行登录
        this.cloudLogin(res.userInfo)
      },
      fail: (err) => {
        console.error('获取用户信息失败', err)
        console.log('微信授权失败，使用模拟登录')
        this.simulateLogin()
      }
    })
  },

  // 模拟登录（开发环境使用）
  simulateLogin() {
    console.log('🔧 开发环境模拟登录')

    // 创建模拟用户信息
    const mockUserInfo = {
      nickName: '开发测试用户',
      avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
      gender: 1,
      country: '中国',
      province: '广东',
      city: '深圳',
      language: 'zh_CN'
    }

    // 保存模拟用户信息
    app.globalData.userInfo = mockUserInfo
    app.globalData.isGuest = false
    wx.setStorageSync('userInfo', mockUserInfo)
    wx.setStorageSync('isGuest', false)

    // 生成模拟openid
    const mockOpenid = 'dev_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    app.globalData.openid = mockOpenid
    wx.setStorageSync('openid', mockOpenid)

    console.log('✅ 模拟登录成功:', mockUserInfo)
    this.loginSuccess()
  },

  // 云开发登录
  cloudLogin(userInfo) {
    // 尝试云函数登录，失败时使用备用方案
    wx.cloud.callFunction({
      name: 'login',
      data: {
        userInfo: userInfo
      },
      success: (res) => {
        console.log('云函数登录成功', res)

        // 保存用户ID
        if (res.result && res.result.openid) {
          app.globalData.openid = res.result.openid
          wx.setStorageSync('openid', res.result.openid)
        }

        this.loginSuccess()
      },
      fail: (err) => {
        console.error('云函数登录失败', err)
        // 使用备用登录方案
        this.fallbackLogin(userInfo)
      }
    })
  },

  // 备用登录方案
  fallbackLogin(userInfo) {
    // 生成模拟的openid
    const mockOpenid = 'mock_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)

    app.globalData.openid = mockOpenid
    wx.setStorageSync('openid', mockOpenid)

    this.loginSuccess()
  },

  // 登录成功处理
  loginSuccess() {
    this.setData({ loading: false })

    wx.showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 跳转到首页
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }, 1500)
  },

  // 游客模式
  guestMode() {
    console.log('游客模式登录')

    // 检查是否在开发环境
    const systemInfo = wx.getSystemInfoSync()
    const isDevelopment = systemInfo.platform === 'devtools'

    if (isDevelopment) {
      console.log('开发环境，直接进入游客模式')
      this.enterGuestMode()
      return
    }

    wx.showModal({
      title: '游客模式',
      content: '游客模式下部分功能将受限，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          this.enterGuestMode()
        }
      }
    })
  },

  // 进入游客模式
  enterGuestMode() {
    // 设置游客标识
    app.globalData.isGuest = true
    app.globalData.userInfo = null
    app.globalData.openid = null

    wx.setStorageSync('isGuest', true)
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('openid')

    console.log('✅ 进入游客模式')

    wx.showToast({
      title: '游客模式',
      icon: 'success'
    })

    setTimeout(() => {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }, 1000)
  }
})
