const app = getApp()

Page({
  data: {
    userInfo: null,
    menus: [
      { name: '我的设备', icon: 'device', url: '/pages/device/device' },
      { name: '检测记录', icon: 'history', url: '/pages/history/history' },
      { name: '设置', icon: 'setting', url: '/pages/settings/settings' }
    ]
  },

  onLoad() {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    } else {
      wx.getUserProfile({
        desc: '用于完善会员资料',
        success: res => {
          app.globalData.userInfo = res.userInfo
          this.setData({ userInfo: res.userInfo })
          wx.setStorageSync('userInfo', res.userInfo)
        },
        fail: () => {
          wx.redirectTo({
            url: '/pages/login/login'
          })
        }
      })
    }
  },

  navigateTo(e) {
    const url = e.currentTarget.dataset.url
    wx.navigateTo({ url })
  },


  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: res => {
        if (res.confirm) {
          app.globalData.userInfo = null
          wx.removeStorageSync('userInfo')
          wx.reLaunch({
            url: '/pages/login/login'
          })
        }
      }
    })
  }
})
