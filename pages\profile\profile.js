const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    userInfo: null,
    totalDetections: 0,
    totalDays: 0,
    forumPosts: 0,
    notificationEnabled: true,
    isGuest: false
  },

  onLoad() {
    this.initUserData()
    this.loadUserStats()
  },

  onShow() {
    this.updateUserInfo()
    this.loadUserStats()
  },

  // 初始化用户数据
  initUserData() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo')
    const isGuest = app.globalData.isGuest || wx.getStorageSync('isGuest')
    const notificationEnabled = wx.getStorageSync('notificationEnabled')

    this.setData({
      userInfo,
      isGuest: isGuest || false,
      notificationEnabled: notificationEnabled !== false // 默认开启
    })
  },

  // 更新用户信息
  updateUserInfo() {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        isGuest: false
      })
    }
  },

  // 加载用户统计数据
  loadUserStats() {
    // 使用示例统计数据，避免云数据库调用
    if (app.globalData.userInfo || app.globalData.isGuest) {
      this.setData({
        totalDetections: 15,
        totalDays: 30,
        forumPosts: 5
      })
    } else {
      this.setData({
        totalDetections: 0,
        totalDays: 1,
        forumPosts: 0
      })
    }
  },

  // 加载检测统计
  loadDetectionStats(openid) {
    db.collection('detections')
      .where({ _openid: openid })
      .count()
      .then(res => {
        this.setData({ totalDetections: res.total })
      })
      .catch(err => {
        console.error('加载检测统计失败:', err)
        this.setData({ totalDetections: 0 })
      })
  },

  // 加载使用天数
  loadUsageDays(openid) {
    // 从用户注册时间计算使用天数
    db.collection('users')
      .where({ _openid: openid })
      .get()
      .then(res => {
        if (res.data.length > 0) {
          const createTime = res.data[0].createTime
          const now = new Date()
          const diffTime = Math.abs(now - new Date(createTime))
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
          this.setData({ totalDays: diffDays })
        } else {
          this.setData({ totalDays: 1 })
        }
      })
      .catch(err => {
        console.error('加载使用天数失败:', err)
        this.setData({ totalDays: 1 })
      })
  },

  // 加载论坛统计
  loadForumStats(openid) {
    db.collection('posts')
      .where({ _openid: openid })
      .count()
      .then(res => {
        this.setData({ forumPosts: res.total })
      })
      .catch(err => {
        console.error('加载论坛统计失败:', err)
        this.setData({ forumPosts: 0 })
      })
  },

  // 快捷功能方法
  viewHistory() {
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  viewFavorites() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  viewReports() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  dataExport() {
    if (!this.data.userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showActionSheet({
      itemList: ['导出Excel', '导出PDF'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.exportData('excel')
        } else if (res.tapIndex === 1) {
          this.exportData('pdf')
        }
      }
    })
  },

  // 导出数据
  exportData(type) {
    wx.showLoading({ title: '导出中...' })

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      })
    }, 2000)
  },

  // 菜单项点击
  editProfile() {
    console.log('编辑个人信息按钮被点击')
    app.safeNavigate({
      url: '/pages/profile/edit',
      isTab: false,
      success: () => {
        console.log('跳转到编辑资料页面成功')
      }
    })
  },

  // 快捷功能
  viewHistory() {
    console.log('查看检测历史')
    app.safeNavigate({
      url: '/pages/history/history',
      isTab: false
    })
  },

  viewFavorites() {
    console.log('查看我的收藏')
    wx.showToast({
      title: '收藏功能开发中',
      icon: 'none'
    })
  },

  viewReports() {
    console.log('查看检测报告')
    wx.showToast({
      title: '报告功能开发中',
      icon: 'none'
    })
  },

  dataExport() {
    console.log('数据导出')
    wx.showActionSheet({
      itemList: ['导出Excel', '导出PDF', '导出CSV'],
      success: (res) => {
        const types = ['Excel', 'PDF', 'CSV']
        wx.showToast({
          title: `${types[res.tapIndex]}导出功能开发中`,
          icon: 'none'
        })
      }
    })
  },

  // 账户管理
  accountSecurity() {
    console.log('账户安全设置')
    wx.showToast({
      title: '安全设置功能开发中',
      icon: 'none'
    })
  },

  privacySettings() {
    console.log('隐私设置')
    wx.showToast({
      title: '隐私设置功能开发中',
      icon: 'none'
    })
  },

  // 应用设置
  systemSettings() {
    console.log('系统设置')
    app.safeNavigate({
      url: '/pages/settings/settings',
      isTab: false
    })
  },

  deviceManagement() {
    console.log('设备管理')
    app.safeNavigate({
      url: '/pages/device/device',
      isTab: true
    })
  },

  notificationSettings() {
    console.log('消息通知设置')
    wx.showToast({
      title: '通知设置功能开发中',
      icon: 'none'
    })
  },

  toggleNotification(e) {
    const enabled = e.detail.value
    this.setData({
      notificationEnabled: enabled
    })
    wx.showToast({
      title: enabled ? '已开启通知' : '已关闭通知',
      icon: 'success'
    })
  },

  // 帮助支持
  userGuide() {
    console.log('使用指南')
    app.safeNavigate({
      url: '/pages/help/help',
      isTab: false
    })
  },

  feedback() {
    console.log('意见反馈')
    wx.showToast({
      title: '反馈功能开发中',
      icon: 'none'
    })
  },

  aboutApp() {
    console.log('关于我们')
    app.safeNavigate({
      url: '/pages/about/about',
      isTab: false
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          app.globalData.userInfo = null
          app.globalData.openid = null
          app.globalData.isGuest = true

          // 清除本地存储
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('openid')
          wx.setStorageSync('isGuest', true)

          // 更新页面状态
          this.setData({
            userInfo: null,
            isGuest: true,
            totalDetections: 0,
            totalDays: 0,
            forumPosts: 0
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 去登录
  goToLogin() {
    app.safeNavigate({
      url: '/pages/login/login',
      isTab: false
    })
  }
})
