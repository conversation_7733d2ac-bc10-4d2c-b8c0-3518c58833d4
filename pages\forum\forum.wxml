<view class="container">
  <!-- 论坛头部 -->
  <view class="forum-header">
    <input
      class="search-input"
      placeholder="搜索帖子..."
      bindinput="onSearchInput"
      value="{{searchKeyword}}"
    />
    <button class="create-btn" bindtap="createPost">
      <view class="create-icon">✏️</view>
      发帖
    </button>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <scroll-view class="tab-scroll" scroll-x="true">
      <view class="tab-list">
        <view
          class="tab-item {{currentCategory === index ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="index"
          bindtap="switchCategory"
          data-index="{{index}}"
        >
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 排序选项 -->
  <view class="sort-options">
    <view class="sort-item {{sortType === 'latest' ? 'active' : ''}}" bindtap="changeSort" data-type="latest">
      <view class="sort-icon">🕒</view>
      最新
    </view>
    <view class="sort-item {{sortType === 'hot' ? 'active' : ''}}" bindtap="changeSort" data-type="hot">
      <view class="sort-icon">🔥</view>
      热门
    </view>
    <view class="sort-item {{sortType === 'reply' ? 'active' : ''}}" bindtap="changeSort" data-type="reply">
      <view class="sort-icon">💬</view>
      回复
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon">⏳</view>
    <text>加载中...</text>
  </view>

  <!-- 帖子列表 -->
  <view class="post-list" wx:else>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{posts.length === 0}}">
      <view class="empty-icon">📝</view>
      <text class="empty-text">暂无帖子</text>
      <button class="create-first-btn" bindtap="createPost">发布第一个帖子</button>
    </view>

    <!-- 帖子项 -->
    <view
      class="post-item"
      wx:for="{{posts}}"
      wx:key="_id"
      bindtap="viewPost"
      data-id="{{item._id}}"
    >
      <!-- 帖子头部 -->
      <view class="post-header">
        <view class="author-info">
          <image class="avatar" src="{{item.avatar || '/assets/icons/default-avatar.png'}}" mode="aspectFit"></image>
          <view class="author-details">
            <text class="author-name">{{item.author}}</text>
            <text class="post-time">{{item.date}}</text>
          </view>
        </view>
        <view class="category-tag {{item.category}}">
          {{item.categoryText}}
        </view>
      </view>

      <!-- 帖子内容 -->
      <view class="post-content">
        <text class="post-title">{{item.title}}</text>
        <text class="post-excerpt">{{item.content}}</text>

        <!-- 图片预览 -->
        <view class="image-preview" wx:if="{{item.images && item.images.length > 0}}">
          <image
            class="preview-image"
            wx:for="{{item.images}}"
            wx:for-item="image"
            wx:key="*this"
            src="{{image}}"
            mode="aspectFill"
            bindtap="previewImage"
            data-urls="{{item.images}}"
            data-current="{{image}}"
          ></image>
        </view>
      </view>

      <!-- 帖子统计 -->
      <view class="post-stats">
        <view class="stat-item">
          <view class="stat-icon">👁️</view>
          <text>{{item.views || 0}}</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon">👍</view>
          <text>{{item.likes || 0}}</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon">💬</view>
          <text>{{item.comments || 0}}</text>
        </view>
        <view class="stat-item">
          <view class="stat-icon">📤</view>
          <text>分享</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{posts.length > 0 && hasMore}}">
    <button class="load-more-btn" bindtap="loadMore" loading="{{loadingMore}}">
      {{loadingMore ? '加载中...' : '加载更多'}}
    </button>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{posts.length > 0 && !hasMore}}">
    <text>没有更多内容了</text>
  </view>
</view>