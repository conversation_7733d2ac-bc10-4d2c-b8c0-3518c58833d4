/* pages/security/security.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.header-left {
  display: flex;
  align-items: center;
  color: #666;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 32rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-right: 80rpx;
}

/* 安全状态概览 */
.security-overview {
  background-color: #fff;
  margin: 20rpx 30rpx;
  padding: 40rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.security-score {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.score-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #2B85E4, #4CAF50);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.score-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.score-label {
  font-size: 24rpx;
  color: #fff;
}

.score-info {
  flex: 1;
}

.score-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.score-desc {
  font-size: 28rpx;
  color: #666;
}

.security-tips {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f0f8ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #2B85E4;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 安全设置列表 */
.security-list {
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.security-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #999;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-action {
  font-size: 28rpx;
  color: #2B85E4;
  margin-right: 10rpx;
}

.item-status {
  font-size: 28rpx;
  color: #999;
}

.item-status.bound {
  color: #4CAF50;
}

.arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 安全建议 */
.security-suggestions {
  background-color: #fff;
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.suggestions-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  margin-top: 2rpx;
}

.suggestion-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 紧急操作 */
.emergency-actions {
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
}

.emergency-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}

.logout-all {
  background-color: #ff9500;
  color: #fff;
}

.freeze-account {
  background-color: #ff4757;
  color: #fff;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  z-index: 1000;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.input-field {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 30rpx;
  background-color: #fafafa;
}

.input-field:focus {
  border-color: #2B85E4;
  background-color: #fff;
}

.code-input-group {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  padding: 20rpx 30rpx;
  background-color: #2B85E4;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
  white-space: nowrap;
}

.send-code-btn[disabled] {
  background-color: #ccc;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  border: none;
}

.modal-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background-color: #2B85E4;
  color: #fff;
}

.modal-btn[disabled] {
  background-color: #ccc;
  color: #999;
}
