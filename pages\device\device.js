const app = getApp()

Page({
  data: {
    devices: [],
    currentDevice: null,
    scanning: false
  },

  onLoad() {
    this.setData({
      currentDevice: app.globalData.currentDevice
    })
  },

  onShow() {
    this.getConnectedDevices()
  },

  getConnectedDevices() {
    wx.getConnectedBluetoothDevices({
      success: res => {
        this.setData({
          devices: res.devices.map(d => ({
            ...d,
            name: d.name || '未知设备'
          }))
        })
      }
    })
  },

  startScan() {
    if (this.data.scanning) return

    this.setData({ scanning: true, devices: [] })
    wx.showLoading({ title: '扫描中...' })

    wx.startBluetoothDevicesDiscovery({
      success: () => {
        wx.onBluetoothDeviceFound(res => {
          const newDevices = res.devices
            .filter(d => d.name && d.name.includes('Bauxite'))
            .map(d => ({
              ...d,
              name: d.name || '未知设备'
            }))

          this.setData({
            devices: [...this.data.devices, ...newDevices]
              .filter((v, i, a) => a.findIndex(t => t.deviceId === v.deviceId) === i)
          })
        })
      },
      complete: () => {
        setTimeout(() => {
          wx.stopBluetoothDevicesDiscovery()
          this.setData({ scanning: false })
          wx.hideLoading()
        }, 5000)
      }
    })
  },

  handleConnect(e) {
    const device = e.currentTarget.dataset.device
    if (this.data.currentDevice === device.deviceId) return

    wx.showLoading({ title: '连接中...' })
    app.connectDevice(device.deviceId)
      .then(() => {
        this.setData({ currentDevice: device.deviceId })
        wx.showToast({ title: '连接成功' })
      })
      .catch(err => {
        console.error(err)
        wx.showToast({ title: '连接失败', icon: 'error' })
      })
      .finally(() => wx.hideLoading())
  },

  // 断开设备连接
  disconnectDevice() {
    wx.showModal({
      title: '断开连接',
      content: '确定要断开当前设备连接吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '断开中...' })

          wx.closeBLEConnection({
            deviceId: this.data.currentDevice,
            success: () => {
              this.setData({ currentDevice: null })
              app.globalData.currentDevice = null
              app.globalData.deviceConnected = false

              wx.showToast({
                title: '已断开连接',
                icon: 'success'
              })
            },
            fail: (err) => {
              console.error('断开连接失败', err)
              wx.showToast({
                title: '断开失败',
                icon: 'error'
              })
            },
            complete: () => {
              wx.hideLoading()
            }
          })
        }
      }
    })
  },

  // 开始检测
  startDetection() {
    if (!this.data.currentDevice) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'error'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/detection/detection'
    })
  },

  // 设备设置
  deviceSettings() {
    if (!this.data.currentDevice) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/device/settings'
    })
  },

  // 设备校准
  deviceCalibration() {
    if (!this.data.currentDevice) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/device/calibration'
    })
  },

  // 设备信息
  deviceInfo() {
    if (!this.data.currentDevice) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/device/info'
    })
  },

  // 使用帮助
  deviceHelp() {
    wx.navigateTo({
      url: '/pages/help/device'
    })
  }
})
