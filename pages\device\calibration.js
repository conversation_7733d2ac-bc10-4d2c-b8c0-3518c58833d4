Page({
  data: {
    calibrationSteps: [
      { id: 1, title: '准备标准样品', status: 'pending', description: '请准备已知成分的标准样品' },
      { id: 2, title: '放入标准样品', status: 'pending', description: '将标准样品放入检测仓' },
      { id: 3, title: '开始校准', status: 'pending', description: '点击开始校准按钮' },
      { id: 4, title: '校准检测', status: 'pending', description: '设备正在进行校准检测' },
      { id: 5, title: '校准完成', status: 'pending', description: '校准参数已保存' }
    ],
    currentStep: 0,
    calibrating: false,
    calibrationResult: null
  },

  onLoad() {
    // 检查设备连接状态
    const deviceConnected = wx.getStorageSync('deviceConnected')
    if (!deviceConnected) {
      wx.showModal({
        title: '设备未连接',
        content: '校准功能需要连接设备，请先连接设备',
        confirmText: '去连接',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/device/device'
            })
          } else {
            wx.navigateBack()
          }
        }
      })
    }
  },

  startCalibration() {
    if (this.data.calibrating) return

    wx.showModal({
      title: '开始校准',
      content: '请确保已放入标准样品，校准过程约需要3-5分钟',
      confirmText: '开始校准',
      success: (res) => {
        if (res.confirm) {
          this.performCalibration()
        }
      }
    })
  },

  performCalibration() {
    this.setData({ 
      calibrating: true,
      currentStep: 0
    })

    this.executeCalibrationStep(0)
  },

  executeCalibrationStep(stepIndex) {
    if (stepIndex >= this.data.calibrationSteps.length) {
      this.calibrationComplete()
      return
    }

    // 更新当前步骤状态
    const steps = this.data.calibrationSteps
    steps[stepIndex].status = 'active'
    this.setData({
      calibrationSteps: steps,
      currentStep: stepIndex
    })

    // 模拟步骤执行时间
    const stepDurations = [1000, 1500, 2000, 5000, 1000]
    
    setTimeout(() => {
      steps[stepIndex].status = 'completed'
      this.setData({ calibrationSteps: steps })
      
      // 执行下一步
      this.executeCalibrationStep(stepIndex + 1)
    }, stepDurations[stepIndex])
  },

  calibrationComplete() {
    const result = {
      success: true,
      alAccuracy: '±0.3%',
      siAccuracy: '±0.2%',
      feAccuracy: '±0.4%',
      calibrationTime: new Date().toLocaleString()
    }

    this.setData({
      calibrating: false,
      calibrationResult: result
    })

    // 保存校准结果
    wx.setStorageSync('lastCalibration', result)

    wx.showToast({
      title: '校准完成',
      icon: 'success'
    })
  },

  resetCalibration() {
    wx.showModal({
      title: '重置校准',
      content: '确定要重新开始校准吗？',
      success: (res) => {
        if (res.confirm) {
          const steps = this.data.calibrationSteps.map(step => ({
            ...step,
            status: 'pending'
          }))
          
          this.setData({
            calibrationSteps: steps,
            currentStep: 0,
            calibrating: false,
            calibrationResult: null
          })
        }
      }
    })
  },

  viewCalibrationHistory() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  }
})
