.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 设备状态卡片 */
.device-status-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.device-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.status-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
}

.status-text.connected {
  color: #4caf50;
}

.status-text.disconnected {
  color: #f44336;
}

.connect-btn {
  background-color: #2B85E4;
  color: white;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  border: none;
}

/* 检测控制区域 */
.detection-control {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.control-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.section-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 检测按钮 */
.detection-button-area {
  text-align: center;
}

.detection-btn {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
}

.detection-btn.active {
  background: linear-gradient(135deg, #2B85E4 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 8rpx 30rpx rgba(43, 133, 228, 0.3);
}

.detection-btn.disabled {
  background-color: #f5f5f5;
  color: #ccc;
}

.btn-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.detection-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 检测进度 */
.detection-progress {
  text-align: center;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.progress-percent {
  font-size: 24rpx;
  color: #2B85E4;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2B85E4 0%, #1976d2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 检测步骤 */
.detection-steps {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.step-item.active {
  background-color: #e3f2fd;
  border: 1rpx solid #2B85E4;
}

.step-item.completed {
  background-color: #e8f5e8;
  border: 1rpx solid #4caf50;
}

.step-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
  background-color: #ddd;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.step-item.active .step-icon {
  background-color: #2B85E4;
}

.step-item.completed .step-icon {
  background-color: #4caf50;
}

.step-icon image {
  width: 24rpx;
  height: 24rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  background-color: white;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.step-name {
  font-size: 26rpx;
  color: #333;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  font-size: 26rpx;
  border: none;
}

/* 检测结果 */
.detection-results {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.grade-badge {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.grade-badge.excellent {
  background-color: #4caf50;
}

.grade-badge.good {
  background-color: #2196f3;
}

.grade-badge.average {
  background-color: #ff9800;
}

.grade-badge.poor {
  background-color: #f44336;
}

/* 主要成分 */
.main-components {
  margin-bottom: 30rpx;
}

.component-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.component-item.primary {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 2rpx solid #2196f3;
}

.component-name {
  font-size: 26rpx;
  color: #666;
}

.component-item.primary .component-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #1976d2;
}

.component-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.component-item.primary .component-value {
  font-size: 36rpx;
  color: #1976d2;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background-color: #2B85E4;
  color: white;
}

.action-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
}

/* 历史记录 */
.history-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.view-all {
  font-size: 24rpx;
  color: #2B85E4;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.history-info {
  display: flex;
  flex-direction: column;
}

.sample-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.detection-time {
  font-size: 22rpx;
  color: #999;
}

.history-result {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.al-content {
  font-size: 24rpx;
  color: #666;
}

.grade-mini {
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  color: white;
}

.grade-mini.excellent {
  background-color: #4caf50;
}

.grade-mini.good {
  background-color: #2196f3;
}

.grade-mini.average {
  background-color: #ff9800;
}

.grade-mini.poor {
  background-color: #f44336;
}

/* 样品信息对话框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.sample-dialog {
  width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin: 0 40rpx;
}

.dialog-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.dialog-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.dialog-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.dialog-content {
  margin-bottom: 40rpx;
}

.input-group {
  margin-bottom: 25rpx;
}

.input-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input-field, .textarea-field {
  width: 100%;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
}

.textarea-field {
  height: 120rpx;
  resize: none;
}

.dialog-actions {
  display: flex;
  gap: 20rpx;
}

.dialog-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  border: none;
}

.dialog-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.dialog-btn.confirm {
  background-color: #2B85E4;
  color: white;
}
