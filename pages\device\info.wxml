<view class="container">
  <!-- 设备基本信息 -->
  <view class="info-section">
    <view class="section-header">
      <view class="device-icon">📱</view>
      <view class="device-basic">
        <text class="device-name">{{deviceInfo.name}}</text>
        <text class="device-model">{{deviceInfo.model}}</text>
      </view>
      <view class="connection-status connected">{{deviceInfo.connectionStatus}}</view>
    </view>
    
    <view class="info-grid">
      <view class="info-item">
        <text class="info-label">序列号</text>
        <text class="info-value">{{deviceInfo.serialNumber}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">固件版本</text>
        <text class="info-value">{{deviceInfo.firmwareVersion}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">硬件版本</text>
        <text class="info-value">{{deviceInfo.hardwareVersion}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">电池电量</text>
        <view class="battery-info">
          <view class="battery-bar">
            <view class="battery-fill" style="width: {{deviceInfo.batteryLevel}}%"></view>
          </view>
          <text class="battery-text">{{deviceInfo.batteryLevel}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用统计 -->
  <view class="stats-section">
    <view class="section-title">使用统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{deviceInfo.totalDetections}}</text>
        <text class="stat-label">总检测次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{deviceInfo.workingHours}}</text>
        <text class="stat-label">工作小时</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{deviceInfo.lastCalibration}}</text>
        <text class="stat-label">最后校准</text>
      </view>
    </view>
  </view>

  <!-- 技术规格 -->
  <view class="specs-section">
    <view class="section-title">技术规格</view>
    <view class="specs-list">
      <view class="spec-item" wx:for="{{specifications}}" wx:key="label">
        <text class="spec-label">{{item.label}}</text>
        <text class="spec-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn primary" bindtap="checkFirmwareUpdate">
      <view class="btn-icon">🔄</view>
      <text>检查固件更新</text>
    </button>
    <button class="action-btn secondary" bindtap="exportDeviceReport">
      <view class="btn-icon">📊</view>
      <text>导出设备报告</text>
    </button>
    <button class="action-btn tertiary" bindtap="contactSupport">
      <view class="btn-icon">💬</view>
      <text>联系技术支持</text>
    </button>
    <button class="action-btn danger" bindtap="resetDevice">
      <view class="btn-icon">⚠️</view>
      <text>重置设备</text>
    </button>
  </view>
</view>
