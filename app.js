App({
  onLaunch() {
    // 初始化云开发
    wx.cloud.init({
      env: 'bauxite-detection-env',
      traceUser: true
    })

    // 检查登录状态
    this.checkLoginStatus()
  },

  globalData: {
    userInfo: null,
    deviceConnected: false,
    currentDevice: null
  },

  checkLoginStatus() {
    // 检查本地存储的登录状态
    wx.getStorage({
      key: 'userInfo',
      success: res => {
        this.globalData.userInfo = res.data
      }
    })
  },

  // 设备连接相关方法
  connectDevice(deviceId) {
    return new Promise((resolve, reject) => {
      wx.connectBLEDevice({
        deviceId,
        success: () => {
          this.globalData.deviceConnected = true
          this.globalData.currentDevice = deviceId
          resolve()
        },
        fail: err => reject(err)
      })
    })
  }
})
