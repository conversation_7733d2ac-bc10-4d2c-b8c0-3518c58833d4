App({
  onLaunch() {
    // 初始化云开发
    wx.cloud.init({
      env: 'bauxite-detection-env',
      traceUser: true
    })

    // 检查登录状态
    this.checkLoginStatus()

    // 初始化蓝牙适配器
    this.initBluetooth()

    // 检查更新
    this.checkForUpdate()
  },

  globalData: {
    userInfo: null,
    openid: null,
    deviceConnected: false,
    currentDevice: null,
    autoConnect: true,
    isGuest: false,
    systemInfo: null
  },

  onShow() {
    // 应用从后台进入前台时触发
    this.checkDeviceConnection()
  },

  onHide() {
    // 应用从前台进入后台时触发
    console.log('应用进入后台')
  },

  // 检查登录状态
  checkLoginStatus() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      const openid = wx.getStorageSync('openid')
      const isGuest = wx.getStorageSync('isGuest')

      if (userInfo) {
        this.globalData.userInfo = userInfo
      }
      if (openid) {
        this.globalData.openid = openid
      }

      // 如果没有明确的游客状态，但也没有用户信息，则设为游客模式
      if (isGuest !== null) {
        this.globalData.isGuest = isGuest
      } else if (!userInfo && !openid) {
        this.globalData.isGuest = true
        wx.setStorageSync('isGuest', true)
      }

      console.log('登录状态检查完成:', {
        hasUserInfo: !!userInfo,
        hasOpenid: !!openid,
        isGuest: this.globalData.isGuest
      })
    } catch (error) {
      console.error('检查登录状态失败:', error)
      // 出错时默认设为游客模式
      this.globalData.isGuest = true
    }
  },

  // 初始化蓝牙适配器
  initBluetooth() {
    wx.openBluetoothAdapter({
      success: () => {
        console.log('蓝牙适配器初始化成功')
        this.globalData.bluetoothAvailable = true
      },
      fail: (err) => {
        console.error('蓝牙适配器初始化失败:', err)
        this.globalData.bluetoothAvailable = false
      }
    })
  },

  // 检查设备连接状态
  checkDeviceConnection() {
    if (this.globalData.currentDevice) {
      wx.getBLEDeviceServices({
        deviceId: this.globalData.currentDevice,
        success: () => {
          this.globalData.deviceConnected = true
        },
        fail: () => {
          this.globalData.deviceConnected = false
          this.globalData.currentDevice = null
        }
      })
    }
  },

  // 设备连接方法
  connectDevice(deviceId) {
    return new Promise((resolve, reject) => {
      wx.connectBLEDevice({
        deviceId,
        success: () => {
          this.globalData.deviceConnected = true
          this.globalData.currentDevice = deviceId

          // 保存到本地存储
          wx.setStorageSync('currentDevice', deviceId)

          console.log('设备连接成功:', deviceId)
          resolve()
        },
        fail: err => {
          console.error('设备连接失败:', err)
          reject(err)
        }
      })
    })
  },

  // 断开设备连接
  disconnectDevice() {
    return new Promise((resolve, reject) => {
      if (!this.globalData.currentDevice) {
        resolve()
        return
      }

      wx.closeBLEConnection({
        deviceId: this.globalData.currentDevice,
        success: () => {
          this.globalData.deviceConnected = false
          this.globalData.currentDevice = null

          // 清除本地存储
          wx.removeStorageSync('currentDevice')

          console.log('设备断开成功')
          resolve()
        },
        fail: err => {
          console.error('设备断开失败:', err)
          reject(err)
        }
      })
    })
  },

  // 获取系统信息
  getSystemInfo() {
    if (!this.globalData.systemInfo) {
      this.globalData.systemInfo = wx.getSystemInfoSync()
    }
    return this.globalData.systemInfo
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()

      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败')
      })
    }
  },

  // 全局错误处理
  onError(error) {
    console.error('小程序发生错误:', error)

    // 可以在这里添加错误上报逻辑
    wx.reportAnalytics('app_error', {
      error: error.toString(),
      timestamp: Date.now()
    })
  },

  // 页面不存在时触发
  onPageNotFound(res) {
    console.error('页面不存在:', res)
    wx.redirectTo({
      url: '/pages/home/<USER>'
    })
  }
})
