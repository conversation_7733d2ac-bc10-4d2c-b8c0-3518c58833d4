<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon">⏳</view>
    <text>加载中...</text>
  </view>

  <!-- 新闻详情 -->
  <view class="news-detail" wx:elif="{{newsDetail}}">
    <!-- 文章头部 -->
    <view class="article-header">
      <text class="article-title">{{newsDetail.title}}</text>
      <view class="article-meta">
        <view class="meta-item">
          <text class="category">{{newsDetail.category}}</text>
        </view>
        <view class="meta-item">
          <text class="author">作者：{{newsDetail.author}}</text>
        </view>
        <view class="meta-item">
          <text class="publish-time">{{newsDetail.publishTime}}</text>
        </view>
        <view class="meta-item">
          <view class="views">
            <view class="view-icon">👁️</view>
            <text>{{newsDetail.views}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 文章内容 -->
    <view class="article-content">
      <text class="content-text">{{newsDetail.content}}</text>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn" open-type="share">
        <view class="btn-icon">📤</view>
        <text>分享</text>
      </button>
      <button class="action-btn" bindtap="goBack">
        <view class="btn-icon">🔙</view>
        <text>返回</text>
      </button>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:else>
    <view class="error-icon">❌</view>
    <text class="error-text">新闻不存在或已删除</text>
    <button class="back-btn" bindtap="goBack">返回列表</button>
  </view>
</view>
