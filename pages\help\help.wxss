.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  padding: 40rpx 0;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
}

.quick-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.quick-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 30rpx 20rpx;
  background-color: white;
  border: none;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.btn-icon {
  font-size: 40rpx;
}

.quick-btn text {
  font-size: 28rpx;
  color: #333;
}

.help-categories {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.category-item {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  cursor: pointer;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.category-icon {
  font-size: 32rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.expand-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.category-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.category-content.show {
  max-height: 1000rpx;
}

.help-item {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.help-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.footer-info {
  text-align: center;
  padding: 40rpx 0;
  margin-top: 40rpx;
}

.version-info, .update-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
