.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.news-detail {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.article-header {
  padding: 40rpx;
  border-bottom: 1rpx solid #eee;
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 30rpx;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
}

.category {
  background-color: #2B85E4;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.author, .publish-time {
  font-size: 26rpx;
  color: #666;
}

.views {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.view-icon {
  font-size: 20rpx;
}

.article-content {
  padding: 40rpx;
}

.content-text {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 40rpx;
  border-top: 1rpx solid #eee;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.action-btn:first-child {
  background-color: #2B85E4;
  color: white;
}

.btn-icon {
  font-size: 24rpx;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.back-btn {
  background-color: #2B85E4;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
