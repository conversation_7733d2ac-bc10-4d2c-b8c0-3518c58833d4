<view class="container">
  <!-- 顶部横幅 -->
  <view class="hero-banner">
    <image class="banner-bg" src="/assets/images/hero-bg.jpg" mode="aspectFill"></image>
    <view class="banner-overlay">
      <view class="banner-content">
        <view class="app-logo">🔬</view>
        <text class="app-title">智能铝土矿检测</text>
        <text class="app-subtitle">专业 · 精准 · 高效</text>
      </view>
    </view>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-card" wx:if="{{userInfo}}">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName}}</text>
        <view class="user-level">
          <text class="vip-icon">👑</text>
          <text>专业用户</text>
        </view>
      </view>
    </view>
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-number">{{totalDetections || 0}}</text>
        <text class="stat-label">检测次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{todayDetections || 0}}</text>
        <text class="stat-label">今日检测</text>
      </view>
    </view>
  </view>

  <!-- 设备连接状态卡片 -->
  <view class="device-status-card">
    <view class="card-header">
      <view class="card-icon">📱</view>
      <view class="card-title">
        <text class="title">设备状态</text>
        <text class="subtitle">智能检测设备连接监控</text>
      </view>
    </view>
    <view class="status-content">
      <view class="status-indicator {{deviceConnected ? 'connected' : 'disconnected'}}">
        <view class="status-dot"></view>
        <text class="status-text">{{deviceConnected ? '设备已连接' : '设备未连接'}}</text>
      </view>
      <button wx:if="{{!deviceConnected}}" class="connect-btn" bindtap="connectDevice">
        <text class="bluetooth-icon">📶</text>
        连接设备
      </button>
      <view wx:else class="device-info">
        <text class="device-name">智能检测仪 Pro</text>
        <text class="device-id">ID: {{currentDevice}}</text>
      </view>
    </view>
  </view>

  <!-- 最新检测数据 -->
  <view class="latest-detection" wx:if="{{latestResult}}">
    <view class="section-header">
      <view class="header-left">
        <view class="section-icon">📊</view>
        <text class="section-title">最新检测结果</text>
      </view>
      <text class="view-detail" bindtap="viewHistory">查看详情</text>
    </view>

    <view class="detection-result-card">
      <view class="result-header">
        <text class="sample-name">{{latestResult.sampleName}}</text>
        <view class="grade-badge {{latestResult.grade}}">{{latestResult.gradeText}}</view>
      </view>

      <view class="components-grid">
        <view class="component-item primary">
          <view class="component-icon">🔵</view>
          <view class="component-info">
            <text class="component-name">铝含量</text>
            <text class="component-value">{{latestResult.alContent}}%</text>
          </view>
        </view>

        <view class="component-item">
          <view class="component-icon">🟡</view>
          <view class="component-info">
            <text class="component-name">硅含量</text>
            <text class="component-value">{{latestResult.siContent}}%</text>
          </view>
        </view>

        <view class="component-item">
          <view class="component-icon">🔴</view>
          <view class="component-info">
            <text class="component-name">铁含量</text>
            <text class="component-value">{{latestResult.feContent}}%</text>
          </view>
        </view>

        <view class="component-item">
          <view class="component-icon">⚪</view>
          <view class="component-info">
            <text class="component-name">钛含量</text>
            <text class="component-value">{{latestResult.tiContent}}%</text>
          </view>
        </view>
      </view>

      <view class="detection-time">
        <text class="time-icon">🕐</text>
        <text>检测时间：{{latestResult.date}}</text>
      </view>
    </view>
  </view>

  <!-- 空状态 - 无检测数据 -->
  <view class="no-data-state" wx:else>
    <view class="no-data-icon">📋</view>
    <text class="no-data-text">暂无检测数据</text>
    <text class="no-data-hint">开始您的第一次检测吧</text>
  </view>

  <!-- 快速操作区域 -->
  <view class="quick-actions-section">
    <view class="section-header">
      <view class="section-icon">⚡</view>
      <text class="section-title">快速操作</text>
    </view>

    <view class="action-buttons">
      <button class="action-btn primary" bindtap="startDetection">
        <view class="btn-content">
          <view class="btn-icon">🔬</view>
          <view class="btn-text">
            <text class="btn-title">开始检测</text>
            <text class="btn-subtitle">智能成分分析</text>
          </view>
        </view>
        <view class="btn-arrow">▶</view>
      </button>

      <view class="secondary-actions">
        <button class="action-btn secondary" bindtap="viewHistory">
          <view class="btn-icon">📊</view>
          <text class="btn-title">历史记录</text>
        </button>

        <button class="action-btn secondary" bindtap="goToDevice">
          <view class="btn-icon">📱</view>
          <text class="btn-title">设备管理</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 功能服务网格 -->
  <view class="services-grid">
    <view class="section-header">
      <view class="section-icon">🔧</view>
      <text class="section-title">专业服务</text>
    </view>

    <view class="grid-container">
      <view class="service-item" bindtap="goToForum">
        <view class="service-icon-wrapper forum">
          <view class="service-icon">💬</view>
        </view>
        <text class="service-name">技术论坛</text>
        <text class="service-desc">专业交流</text>
      </view>

      <view class="service-item" bindtap="dataAnalysis">
        <view class="service-icon-wrapper analysis">
          <view class="service-icon">📈</view>
        </view>
        <text class="service-name">数据分析</text>
        <text class="service-desc">趋势报告</text>
      </view>

      <view class="service-item" bindtap="qualityReport">
        <view class="service-icon-wrapper report">
          <view class="service-icon">📋</view>
        </view>
        <text class="service-name">质量报告</text>
        <text class="service-desc">专业评估</text>
      </view>

      <view class="service-item" bindtap="goToSettings">
        <view class="service-icon-wrapper settings">
          <view class="service-icon">⚙️</view>
        </view>
        <text class="service-name">系统设置</text>
        <text class="service-desc">个性配置</text>
      </view>
    </view>
  </view>

  <!-- 行业资讯 -->
  <view class="industry-news">
    <view class="section-header">
      <view class="section-icon">📰</view>
      <text class="section-title">行业动态</text>
      <text class="view-more" bindtap="viewMoreNews">更多</text>
    </view>

    <view class="news-list">
      <view class="news-item" wx:for="{{industryNews}}" wx:key="id" bindtap="viewNewsDetail" data-id="{{item.id}}">
        <image class="news-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="news-content">
          <text class="news-title">{{item.title}}</text>
          <text class="news-summary">{{item.summary}}</text>
          <view class="news-meta">
            <text class="news-date">{{item.date}}</text>
            <text class="news-source">{{item.source}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
