<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section" wx:if="{{userInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>

  <!-- 设备连接状态 -->
  <view class="device-status">
    <view class="status-card {{deviceConnected ? 'connected' : 'disconnected'}}">
      <text>设备状态: {{deviceConnected ? '已连接' : '未连接'}}</text>
      <button wx:if="{{!deviceConnected}}" bindtap="connectDevice">连接设备</button>
    </view>
  </view>

  <!-- 检测数据概览 -->
  <view class="data-overview">
    <view class="data-card">
      <text class="title">铝含量</text>
      <text class="value">{{alContent || '--'}}%</text>
    </view>
    <view class="data-card">
      <text class="title">硅含量</text>
      <text class="value">{{siContent || '--'}}%</text>
    </view>
    <view class="data-card">
      <text class="title">铁含量</text>
      <text class="value">{{feContent || '--'}}%</text>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <button class="primary-btn" bindtap="startDetection">
      <image src="/assets/icons/detection.png" mode="aspectFit"></image>
      开始检测
    </button>
    <button class="secondary-btn" bindtap="viewHistory">
      <image src="/assets/icons/history.png" mode="aspectFit"></image>
      历史记录
    </button>
  </view>

  <!-- 功能菜单 -->
  <view class="function-menu">
    <view class="menu-title">更多功能</view>
    <view class="menu-grid">
      <view class="menu-item" bindtap="goToDevice">
        <image class="menu-icon" src="/assets/icons/device.png" mode="aspectFit"></image>
        <text class="menu-text">设备管理</text>
      </view>
      <view class="menu-item" bindtap="goToForum">
        <image class="menu-icon" src="/assets/icons/forum.png" mode="aspectFit"></image>
        <text class="menu-text">交流论坛</text>
      </view>
      <view class="menu-item" bindtap="goToSettings">
        <image class="menu-icon" src="/assets/icons/settings.png" mode="aspectFit"></image>
        <text class="menu-text">系统设置</text>
      </view>
      <view class="menu-item" bindtap="goToHelp">
        <image class="menu-icon" src="/assets/icons/help.png" mode="aspectFit"></image>
        <text class="menu-text">使用帮助</text>
      </view>
    </view>
  </view>
</view>
