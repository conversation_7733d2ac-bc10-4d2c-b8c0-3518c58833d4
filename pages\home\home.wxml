<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section" wx:if="{{userInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>

  <!-- 设备连接状态 -->
  <view class="device-status">
    <view class="status-card {{deviceConnected ? 'connected' : 'disconnected'}}">
      <text>设备状态: {{deviceConnected ? '已连接' : '未连接'}}</text>
      <button wx:if="{{!deviceConnected}}" bindtap="connectDevice">连接设备</button>
    </view>
  </view>

  <!-- 检测数据概览 -->
  <view class="data-overview">
    <view class="data-card">
      <text class="title">铝含量</text>
      <text class="value">{{alContent || '--'}}%</text>
    </view>
    <view class="data-card">
      <text class="title">硅含量</text>
      <text class="value">{{siContent || '--'}}%</text>
    </view>
    <view class="data-card">
      <text class="title">铁含量</text>
      <text class="value">{{feContent || '--'}}%</text>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <button bindtap="startDetection">开始检测</button>
    <button bindtap="viewHistory">历史记录</button>
  </view>
</view>
