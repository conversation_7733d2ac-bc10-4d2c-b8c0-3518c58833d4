const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    posts: [],
    loading: true,
    searchKeyword: '',
    currentCategory: 0,
    categories: [
      { name: '全部', value: 'all' },
      { name: '技术交流', value: 'tech' },
      { name: '经验分享', value: 'experience' },
      { name: '问题求助', value: 'help' },
      { name: '设备讨论', value: 'device' },
      { name: '行业动态', value: 'news' }
    ],
    sortType: 'latest',
    page: 1,
    pageSize: 10,
    hasMore: true,
    loadingMore: false
  },

  onLoad() {
    this.loadPosts()
  },

  onShow() {
    // 从发帖页面返回时刷新列表
    this.loadPosts()
  },

  // 加载帖子列表
  loadPosts(refresh = true) {
    if (refresh) {
      this.setData({
        loading: true,
        page: 1,
        hasMore: true,
        posts: []
      })
    } else {
      this.setData({ loadingMore: true })
    }

    // 直接使用示例数据，避免云数据库操作失败
    this.loadSamplePosts(refresh)
  },

  // 加载示例帖子数据
  loadSamplePosts(refresh = true) {
    // 生成示例帖子数据
    const samplePosts = [
      {
        _id: 'sample_1',
        title: '铝土矿检测设备使用心得分享',
        content: '最近使用了新的检测设备，在精度和效率方面都有很大提升。特别是在铝含量检测方面，误差控制在±0.5%以内，比之前的设备提升了不少。',
        category: 'experience',
        categoryText: '经验分享',
        author: '矿物专家',
        avatar: '',
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
        views: 156,
        likes: 23,
        comments: 8,
        images: []
      },
      {
        _id: 'sample_2',
        title: '关于硅含量检测异常的问题求助',
        content: '最近在检测过程中发现硅含量数据波动较大，不知道是设备问题还是样品处理问题，有遇到类似情况的朋友吗？',
        category: 'help',
        categoryText: '问题求助',
        author: '新手小白',
        avatar: '',
        createTime: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5小时前
        views: 89,
        likes: 12,
        comments: 15,
        images: []
      },
      {
        _id: 'sample_3',
        title: '2024年铝土矿行业发展趋势分析',
        content: '根据最新的市场调研数据，今年铝土矿行业呈现出新的发展趋势，智能化检测设备需求大幅增长...',
        category: 'news',
        categoryText: '行业动态',
        author: '行业分析师',
        avatar: '',
        createTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1天前
        views: 234,
        likes: 45,
        comments: 22,
        images: []
      },
      {
        _id: 'sample_4',
        title: '便携式检测设备对比评测',
        content: '对市面上几款主流的便携式铝土矿检测设备进行了详细对比，从精度、便携性、价格等多个维度进行分析...',
        category: 'device',
        categoryText: '设备讨论',
        author: '设备评测员',
        avatar: '',
        createTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
        views: 178,
        likes: 31,
        comments: 19,
        images: []
      },
      {
        _id: 'sample_5',
        title: '检测数据异常处理的技术方案',
        content: '在实际检测过程中，经常会遇到数据异常的情况。本文总结了几种常见的异常处理方法和技术方案...',
        category: 'tech',
        categoryText: '技术交流',
        author: '技术大牛',
        avatar: '',
        createTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3天前
        views: 312,
        likes: 67,
        comments: 34,
        images: []
      }
    ]

    // 添加本地帖子
    const localPosts = wx.getStorageSync('localPosts') || []
    const allPosts = [...localPosts, ...samplePosts]

    // 应用筛选和排序
    let filteredPosts = allPosts

    // 分类筛选
    const currentCategory = this.data.categories[this.data.currentCategory]
    if (currentCategory.value !== 'all') {
      filteredPosts = filteredPosts.filter(post => post.category === currentCategory.value)
    }

    // 搜索筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase()
      filteredPosts = filteredPosts.filter(post =>
        post.title.toLowerCase().includes(keyword) ||
        post.content.toLowerCase().includes(keyword)
      )
    }

    // 排序
    switch (this.data.sortType) {
      case 'latest':
        filteredPosts.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
        break
      case 'hot':
        filteredPosts.sort((a, b) => (b.views || 0) - (a.views || 0))
        break
      case 'reply':
        filteredPosts.sort((a, b) => (b.comments || 0) - (a.comments || 0))
        break
    }

    // 格式化数据
    const formattedPosts = filteredPosts.map(post => ({
      ...post,
      date: this.formatDate(post.createTime),
      categoryText: this.getCategoryText(post.category)
    }))

    // 分页处理
    const startIndex = refresh ? 0 : this.data.posts.length
    const endIndex = startIndex + this.data.pageSize
    const newPosts = formattedPosts.slice(startIndex, endIndex)
    const hasMore = endIndex < formattedPosts.length

    const posts = refresh ? newPosts : [...this.data.posts, ...newPosts]

    this.setData({
      posts,
      hasMore,
      loading: false,
      loadingMore: false,
      page: this.data.page + (refresh ? 0 : 1)
    })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value })

    // 防抖搜索
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.loadPosts()
    }, 500)
  },

  // 切换分类
  switchCategory(e) {
    const index = e.currentTarget.dataset.index
    this.setData({ currentCategory: index })
    this.loadPosts()
  },

  // 改变排序方式
  changeSort(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ sortType: type })
    this.loadPosts()
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loadingMore) return
    this.loadPosts(false)
  },

  // 查看帖子详情
  viewPost(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/post/detail?id=${id}`
    })
  },

  // 预览图片
  previewImage(e) {
    const urls = e.currentTarget.dataset.urls
    const current = e.currentTarget.dataset.current

    wx.previewImage({
      urls,
      current
    })
  },

  // 获取分类文本
  getCategoryText(category) {
    const categoryMap = {
      'tech': '技术交流',
      'experience': '经验分享',
      'help': '问题求助',
      'device': '设备讨论',
      'news': '行业动态'
    }
    return categoryMap[category] || '其他'
  },

  // 格式化日期
  formatDate(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}-${day}`
    }
  },

  // 创建帖子
  createPost() {
    if (!app.globalData.userInfo) {
      wx.showToast({ title: '请先登录', icon: 'error' })
      return
    }
    wx.navigateTo({
      url: '/pages/post/create'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadPosts()
    wx.stopPullDownRefresh()
  },

  // 触底加载更多
  onReachBottom() {
    this.loadMore()
  }
})
