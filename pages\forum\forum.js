const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    posts: [],
    loading: true,
    searchKeyword: '',
    currentCategory: 0,
    categories: [
      { name: '全部', value: 'all' },
      { name: '技术交流', value: 'tech' },
      { name: '经验分享', value: 'experience' },
      { name: '问题求助', value: 'help' },
      { name: '设备讨论', value: 'device' },
      { name: '行业动态', value: 'news' }
    ],
    sortType: 'latest',
    page: 1,
    pageSize: 10,
    hasMore: true,
    loadingMore: false
  },

  onLoad() {
    this.loadPosts()
  },

  onShow() {
    // 从发帖页面返回时刷新列表
    if (this.data.posts.length > 0) {
      this.loadPosts()
    }
  },

  // 加载帖子列表
  loadPosts(refresh = true) {
    if (refresh) {
      this.setData({
        loading: true,
        page: 1,
        hasMore: true,
        posts: []
      })
    } else {
      this.setData({ loadingMore: true })
    }

    let query = db.collection('posts')

    // 分类筛选
    const currentCategory = this.data.categories[this.data.currentCategory]
    if (currentCategory.value !== 'all') {
      query = query.where({
        category: currentCategory.value
      })
    }

    // 搜索关键词
    if (this.data.searchKeyword) {
      query = query.where({
        title: db.RegExp({
          regexp: this.data.searchKeyword,
          options: 'i'
        })
      })
    }

    // 排序
    switch (this.data.sortType) {
      case 'latest':
        query = query.orderBy('createTime', 'desc')
        break
      case 'hot':
        query = query.orderBy('views', 'desc')
        break
      case 'reply':
        query = query.orderBy('comments', 'desc')
        break
    }

    // 分页
    query = query.skip((this.data.page - 1) * this.data.pageSize)
                 .limit(this.data.pageSize)

    query.get()
      .then(res => {
        const newPosts = res.data.map(post => ({
          ...post,
          date: this.formatDate(post.createTime),
          categoryText: this.getCategoryText(post.category)
        }))

        const posts = refresh ? newPosts : [...this.data.posts, ...newPosts]
        const hasMore = newPosts.length === this.data.pageSize

        this.setData({
          posts,
          hasMore,
          loading: false,
          loadingMore: false,
          page: this.data.page + (refresh ? 0 : 1)
        })
      })
      .catch(err => {
        console.error('加载帖子失败', err)
        wx.showToast({ title: '加载失败', icon: 'error' })
        this.setData({
          loading: false,
          loadingMore: false
        })
      })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value })

    // 防抖搜索
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.loadPosts()
    }, 500)
  },

  // 切换分类
  switchCategory(e) {
    const index = e.currentTarget.dataset.index
    this.setData({ currentCategory: index })
    this.loadPosts()
  },

  // 改变排序方式
  changeSort(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ sortType: type })
    this.loadPosts()
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loadingMore) return
    this.loadPosts(false)
  },

  // 查看帖子详情
  viewPost(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/post/detail?id=${id}`
    })
  },

  // 预览图片
  previewImage(e) {
    const urls = e.currentTarget.dataset.urls
    const current = e.currentTarget.dataset.current

    wx.previewImage({
      urls,
      current
    })
  },

  // 获取分类文本
  getCategoryText(category) {
    const categoryMap = {
      'tech': '技术交流',
      'experience': '经验分享',
      'help': '问题求助',
      'device': '设备讨论',
      'news': '行业动态'
    }
    return categoryMap[category] || '其他'
  },

  // 格式化日期
  formatDate(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}-${day}`
    }
  },

  // 创建帖子
  createPost() {
    if (!app.globalData.userInfo) {
      wx.showToast({ title: '请先登录', icon: 'error' })
      return
    }
    wx.navigateTo({
      url: '/pages/post/create'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadPosts()
    wx.stopPullDownRefresh()
  },

  // 触底加载更多
  onReachBottom() {
    this.loadMore()
  }

  viewPost(e) {
    const postId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/post/detail?id=${postId}`
    })
  }
})
