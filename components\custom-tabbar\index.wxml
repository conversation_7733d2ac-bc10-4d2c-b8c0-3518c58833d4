<view class="tab-bar">
  <view
    wx:for="{{list}}"
    wx:key="index"
    class="tab-bar-item {{selected === index ? 'tab-bar-item-active' : ''}}"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
    style="min-height: 100rpx; cursor: pointer;"
  >
    <view class="tab-icon">
      <text class="icon-text {{selected === index ? 'icon-active' : ''}}">
        {{selected === index ? item.selectedIcon : item.icon}}
      </text>
    </view>
    <text class="tab-text {{selected === index ? 'text-active' : ''}}">
      {{item.text}}
    </text>
  </view>
</view>
