<view class="container">
  <!-- 导航栏 -->
  <view class="header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <view class="header-title">账户安全</view>
  </view>

  <!-- 安全状态概览 -->
  <view class="security-overview">
    <view class="security-score">
      <view class="score-circle">
        <text class="score-number">{{securityScore}}</text>
        <text class="score-label">分</text>
      </view>
      <view class="score-info">
        <text class="score-title">安全评分</text>
        <text class="score-desc">{{securityLevel}}</text>
      </view>
    </view>
    <view class="security-tips">
      <text class="tips-icon">💡</text>
      <text class="tips-text">{{securityTip}}</text>
    </view>
  </view>

  <!-- 安全设置列表 -->
  <view class="security-list">
    <!-- 登录密码 -->
    <view class="security-item" bindtap="changePassword">
      <view class="item-left">
        <text class="item-icon">🔐</text>
        <view class="item-info">
          <text class="item-title">登录密码</text>
          <text class="item-desc">{{passwordStatus}}</text>
        </view>
      </view>
      <view class="item-right">
        <text class="item-action">修改</text>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 手机号绑定 -->
    <view class="security-item" bindtap="bindPhone">
      <view class="item-left">
        <text class="item-icon">📱</text>
        <view class="item-info">
          <text class="item-title">手机号</text>
          <text class="item-desc">{{phoneStatus}}</text>
        </view>
      </view>
      <view class="item-right">
        <text class="item-action">{{phoneNumber ? '更换' : '绑定'}}</text>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 邮箱绑定 -->
    <view class="security-item" bindtap="bindEmail">
      <view class="item-left">
        <text class="item-icon">📧</text>
        <view class="item-info">
          <text class="item-title">邮箱</text>
          <text class="item-desc">{{emailStatus}}</text>
        </view>
      </view>
      <view class="item-right">
        <text class="item-action">{{email ? '更换' : '绑定'}}</text>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 微信绑定 -->
    <view class="security-item">
      <view class="item-left">
        <text class="item-icon">💬</text>
        <view class="item-info">
          <text class="item-title">微信账号</text>
          <text class="item-desc">{{wechatStatus}}</text>
        </view>
      </view>
      <view class="item-right">
        <text class="item-status bound">已绑定</text>
      </view>
    </view>

    <!-- 两步验证 -->
    <view class="security-item" bindtap="toggleTwoFactor">
      <view class="item-left">
        <text class="item-icon">🛡️</text>
        <view class="item-info">
          <text class="item-title">两步验证</text>
          <text class="item-desc">登录时需要额外验证</text>
        </view>
      </view>
      <view class="item-right">
        <switch checked="{{twoFactorEnabled}}" bindchange="onTwoFactorChange" color="#2B85E4"/>
      </view>
    </view>

    <!-- 登录设备管理 -->
    <view class="security-item" bindtap="manageDevices">
      <view class="item-left">
        <text class="item-icon">📲</text>
        <view class="item-info">
          <text class="item-title">登录设备</text>
          <text class="item-desc">管理已登录的设备</text>
        </view>
      </view>
      <view class="item-right">
        <text class="item-action">管理</text>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 登录记录 -->
    <view class="security-item" bindtap="viewLoginHistory">
      <view class="item-left">
        <text class="item-icon">📋</text>
        <view class="item-info">
          <text class="item-title">登录记录</text>
          <text class="item-desc">查看最近登录记录</text>
        </view>
      </view>
      <view class="item-right">
        <text class="item-action">查看</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 安全建议 -->
  <view class="security-suggestions">
    <view class="suggestions-title">安全建议</view>
    <view class="suggestion-item" wx:for="{{suggestions}}" wx:key="index">
      <text class="suggestion-icon">{{item.icon}}</text>
      <text class="suggestion-text">{{item.text}}</text>
    </view>
  </view>

  <!-- 紧急操作 -->
  <view class="emergency-actions">
    <button class="emergency-btn logout-all" bindtap="logoutAllDevices">
      退出所有设备
    </button>
    <button class="emergency-btn freeze-account" bindtap="freezeAccount">
      冻结账户
    </button>
  </view>
</view>

<!-- 修改密码弹窗 -->
<view class="modal {{showPasswordModal ? 'show' : ''}}" bindtap="hidePasswordModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">修改密码</text>
      <text class="modal-close" bindtap="hidePasswordModal">×</text>
    </view>
    <view class="modal-body">
      <view class="input-group">
        <text class="input-label">当前密码</text>
        <input class="input-field" type="password" placeholder="请输入当前密码" value="{{currentPassword}}" bindinput="onCurrentPasswordInput"/>
      </view>
      <view class="input-group">
        <text class="input-label">新密码</text>
        <input class="input-field" type="password" placeholder="请输入新密码" value="{{newPassword}}" bindinput="onNewPasswordInput"/>
      </view>
      <view class="input-group">
        <text class="input-label">确认新密码</text>
        <input class="input-field" type="password" placeholder="请再次输入新密码" value="{{confirmPassword}}" bindinput="onConfirmPasswordInput"/>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hidePasswordModal">取消</button>
      <button class="modal-btn confirm" bindtap="confirmChangePassword" disabled="{{!canChangePassword}}">确认</button>
    </view>
  </view>
</view>

<!-- 绑定手机弹窗 -->
<view class="modal {{showPhoneModal ? 'show' : ''}}" bindtap="hidePhoneModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{phoneNumber ? '更换' : '绑定'}}手机号</text>
      <text class="modal-close" bindtap="hidePhoneModal">×</text>
    </view>
    <view class="modal-body">
      <view class="input-group">
        <text class="input-label">手机号</text>
        <input class="input-field" type="number" placeholder="请输入手机号" value="{{inputPhone}}" bindinput="onPhoneInput"/>
      </view>
      <view class="input-group">
        <text class="input-label">验证码</text>
        <view class="code-input-group">
          <input class="input-field code-input" type="number" placeholder="请输入验证码" value="{{phoneCode}}" bindinput="onPhoneCodeInput"/>
          <button class="send-code-btn" bindtap="sendPhoneCode" disabled="{{!canSendPhoneCode || phoneCodeCountdown > 0}}">
            {{phoneCodeCountdown > 0 ? phoneCodeCountdown + 's' : '发送验证码'}}
          </button>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hidePhoneModal">取消</button>
      <button class="modal-btn confirm" bindtap="confirmBindPhone" disabled="{{!canBindPhone}}">确认</button>
    </view>
  </view>
</view>
