<view class="container" wx:if="{{post}}">
  <!-- 导航栏 -->
  <view class="header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <view class="header-title">帖子详情</view>
    <view class="header-right" bindtap="sharePost">
      <text class="share-icon">📤</text>
    </view>
  </view>

  <!-- 帖子内容 -->
  <view class="post-detail">
    <!-- 帖子头部 -->
    <view class="post-header">
      <view class="author-info">
        <view class="avatar">
          <image wx:if="{{post.author.avatar}}" class="avatar-img" src="{{post.author.avatar}}" mode="aspectFill"/>
          <view wx:else class="default-avatar">👤</view>
        </view>
        <view class="author-details">
          <text class="author-name">{{post.author.nickname}}</text>
          <text class="post-time">{{post.createTime}}</text>
        </view>
      </view>
      <view class="category-tag">{{post.categoryText}}</view>
    </view>

    <!-- 帖子标题 -->
    <view class="post-title">{{post.title}}</view>

    <!-- 帖子内容 -->
    <view class="post-content">
      <text class="content-text">{{post.content}}</text>
    </view>

    <!-- 帖子图片 -->
    <view class="post-images" wx:if="{{post.images && post.images.length > 0}}">
      <image
        class="post-image"
        wx:for="{{post.images}}"
        wx:key="index"
        src="{{item}}"
        mode="aspectFill"
        bindtap="previewImage"
        data-urls="{{post.images}}"
        data-current="{{item}}"
      />
    </view>

    <!-- 帖子标签 -->
    <view class="post-tags" wx:if="{{post.tags && post.tags.length > 0}}">
      <view class="tag-item" wx:for="{{post.tags}}" wx:key="index">
        #{{item}}
      </view>
    </view>

    <!-- 帖子统计 -->
    <view class="post-stats">
      <view class="stat-item">
        <text class="stat-icon">👁️</text>
        <text class="stat-text">{{post.viewCount || 0}} 浏览</text>
      </view>
      <view class="stat-item">
        <text class="stat-icon">👍</text>
        <text class="stat-text">{{post.likeCount || 0}} 点赞</text>
      </view>
      <view class="stat-item">
        <text class="stat-icon">💬</text>
        <text class="stat-text">{{post.commentCount || 0}} 评论</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="action-btn like-btn {{liked ? 'liked' : ''}}" bindtap="toggleLike">
      <text class="btn-icon">{{liked ? '❤️' : '🤍'}}</text>
      <text class="btn-text">{{liked ? '已赞' : '点赞'}}</text>
    </button>
    <button class="action-btn comment-btn" bindtap="showCommentInput">
      <text class="btn-icon">💬</text>
      <text class="btn-text">评论</text>
    </button>
    <button class="action-btn share-btn" bindtap="sharePost">
      <text class="btn-icon">📤</text>
      <text class="btn-text">分享</text>
    </button>
  </view>

  <!-- 评论区 -->
  <view class="comments-section">
    <view class="section-title">评论 ({{comments.length}})</view>

    <!-- 评论列表 -->
    <view class="comment-list" wx:if="{{comments.length > 0}}">
      <view class="comment-item" wx:for="{{comments}}" wx:key="id">
        <view class="comment-avatar">
          <image wx:if="{{item.author.avatar}}" class="avatar-img" src="{{item.author.avatar}}" mode="aspectFill"/>
          <view wx:else class="default-avatar">👤</view>
        </view>
        <view class="comment-content">
          <view class="comment-header">
            <text class="comment-author">{{item.author.nickname}}</text>
            <text class="comment-time">{{item.createTime}}</text>
          </view>
          <text class="comment-text">{{item.content}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-comments" wx:else>
      <text class="empty-text">暂无评论，快来抢沙发吧~</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:else>
  <view class="loading-icon">⏳</view>
  <text class="loading-text">加载中...</text>
</view>

<!-- 评论输入框 -->
<view class="comment-input-modal {{showCommentModal ? 'show' : ''}}" bindtap="hideCommentInput">
  <view class="comment-input-content" catchtap="stopPropagation">
    <textarea
      class="comment-textarea"
      placeholder="写下你的评论..."
      value="{{commentText}}"
      bindinput="onCommentInput"
      auto-focus="{{showCommentModal}}"
    />
    <view class="comment-actions">
      <button class="cancel-comment-btn" bindtap="hideCommentInput">取消</button>
      <button class="submit-comment-btn" bindtap="submitComment" disabled="{{!commentText.trim()}}">发布</button>
    </view>
  </view>
</view>