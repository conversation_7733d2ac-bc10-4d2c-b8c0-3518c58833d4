.container {
  background: linear-gradient(180deg, #f8fafe 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 用户信息头部 */
.profile-header {
  position: relative;
  height: 400rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.3;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(43, 133, 228, 0.8) 0%, rgba(25, 118, 210, 0.9) 100%);
  padding: 60rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
  color: white;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 25rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar image {
  width: 100%;
  height: 100%;
}

.default-avatar {
  background: rgba(255, 255, 255, 0.2);
  font-size: 60rpx;
  color: white;
}

.user-details {
  flex: 1;
}

.nickname {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.user-id {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
  margin-bottom: 15rpx;
}

.user-level {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  align-self: flex-start;
}

.vip-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

.user-level text {
  font-size: 22rpx;
  font-weight: 500;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 25rpx 0;
  backdrop-filter: blur(10rpx);
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.9;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.3);
}

/* 快捷功能 */
.quick-functions {
  display: flex;
  justify-content: space-around;
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.function-icon {
  font-size: 60rpx;
  margin-bottom: 12rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-text {
  font-size: 22rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  margin: 0 30rpx 30rpx;
}

.menu-group {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.group-title {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx 20rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #f0f0f0;
}

.group-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.group-title text {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.menu-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-left text {
  font-size: 28rpx;
  color: #333;
}

.item-right {
  display: flex;
  align-items: center;
}

.version {
  font-size: 22rpx;
  color: #999;
  margin-right: 15rpx;
}

.arrow {
  font-size: 20rpx;
  opacity: 0.5;
  color: #999;
}

/* 退出登录 */
.logout-section {
  margin: 0 30rpx 30rpx;
}

.logout-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #ff5722 0%, #d32f2f 100%);
  color: white;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(255, 87, 34, 0.3);
}

/* 登录提示 */
.login-prompt {
  margin: 0 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.login-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
  display: block;
}

.prompt-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.login-btn {
  background: linear-gradient(135deg, #2B85E4 0%, #1976d2 100%);
  color: white;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(43, 133, 228, 0.3);
}
