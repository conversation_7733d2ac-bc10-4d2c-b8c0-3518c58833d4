.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  padding: 40rpx 0;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.help-sections {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.help-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #2B85E4;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #2B85E4;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

.support-section {
  text-align: center;
}

.support-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  background-color: #2B85E4;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 30rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.btn-icon {
  font-size: 36rpx;
}
