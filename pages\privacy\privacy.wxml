<view class="container">
  <!-- 导航栏 -->
  <view class="header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <view class="header-title">隐私设置</view>
  </view>

  <!-- 隐私概览 -->
  <view class="privacy-overview">
    <view class="overview-icon">🛡️</view>
    <view class="overview-content">
      <text class="overview-title">保护您的隐私</text>
      <text class="overview-desc">我们致力于保护您的个人信息安全</text>
    </view>
  </view>

  <!-- 个人信息设置 -->
  <view class="section">
    <view class="section-title">个人信息</view>
    <view class="setting-list">
      <!-- 个人资料可见性 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">👤</text>
          <view class="item-info">
            <text class="item-title">个人资料可见性</text>
            <text class="item-desc">控制谁可以查看您的个人资料</text>
          </view>
        </view>
        <view class="item-right" bindtap="setProfileVisibility">
          <text class="item-value">{{profileVisibility}}</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 头像显示 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📷</text>
          <view class="item-info">
            <text class="item-title">头像显示</text>
            <text class="item-desc">是否在论坛中显示真实头像</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{showAvatar}}" bindchange="onShowAvatarChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 昵称显示 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📝</text>
          <view class="item-info">
            <text class="item-title">昵称显示</text>
            <text class="item-desc">是否在论坛中显示真实昵称</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{showNickname}}" bindchange="onShowNicknameChange" color="#2B85E4"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 活动隐私 -->
  <view class="section">
    <view class="section-title">活动隐私</view>
    <view class="setting-list">
      <!-- 在线状态 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">🟢</text>
          <view class="item-info">
            <text class="item-title">在线状态</text>
            <text class="item-desc">是否显示在线状态</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{showOnlineStatus}}" bindchange="onOnlineStatusChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 最后活动时间 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">⏰</text>
          <view class="item-info">
            <text class="item-title">最后活动时间</text>
            <text class="item-desc">是否显示最后活动时间</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{showLastActive}}" bindchange="onLastActiveChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 帖子历史 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📋</text>
          <view class="item-info">
            <text class="item-title">帖子历史</text>
            <text class="item-desc">是否允许他人查看您的帖子历史</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{showPostHistory}}" bindchange="onPostHistoryChange" color="#2B85E4"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据使用 -->
  <view class="section">
    <view class="section-title">数据使用</view>
    <view class="setting-list">
      <!-- 数据分析 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📊</text>
          <view class="item-info">
            <text class="item-title">数据分析</text>
            <text class="item-desc">允许使用您的数据进行产品改进</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{allowAnalytics}}" bindchange="onAnalyticsChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 个性化推荐 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">🎯</text>
          <view class="item-info">
            <text class="item-title">个性化推荐</text>
            <text class="item-desc">基于您的行为提供个性化内容</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{allowPersonalization}}" bindchange="onPersonalizationChange" color="#2B85E4"/>
        </view>
      </view>

      <!-- 广告个性化 -->
      <view class="setting-item">
        <view class="item-left">
          <text class="item-icon">📢</text>
          <view class="item-info">
            <text class="item-title">广告个性化</text>
            <text class="item-desc">显示与您兴趣相关的广告</text>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{allowPersonalizedAds}}" bindchange="onPersonalizedAdsChange" color="#2B85E4"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="section">
    <view class="section-title">数据管理</view>
    <view class="setting-list">
      <!-- 下载数据 -->
      <view class="setting-item" bindtap="downloadData">
        <view class="item-left">
          <text class="item-icon">📥</text>
          <view class="item-info">
            <text class="item-title">下载我的数据</text>
            <text class="item-desc">获取您在平台上的所有数据副本</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">下载</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 删除数据 -->
      <view class="setting-item" bindtap="deleteData">
        <view class="item-left">
          <text class="item-icon">🗑️</text>
          <view class="item-info">
            <text class="item-title">删除我的数据</text>
            <text class="item-desc">永久删除您的账户和所有数据</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action danger">删除</text>
          <text class="arrow">></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 隐私政策 -->
  <view class="section">
    <view class="section-title">了解更多</view>
    <view class="setting-list">
      <!-- 隐私政策 -->
      <view class="setting-item" bindtap="viewPrivacyPolicy">
        <view class="item-left">
          <text class="item-icon">📄</text>
          <view class="item-info">
            <text class="item-title">隐私政策</text>
            <text class="item-desc">了解我们如何处理您的信息</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">查看</text>
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 使用条款 -->
      <view class="setting-item" bindtap="viewTerms">
        <view class="item-left">
          <text class="item-icon">📋</text>
          <view class="item-info">
            <text class="item-title">使用条款</text>
            <text class="item-desc">查看服务使用条款</text>
          </view>
        </view>
        <view class="item-right">
          <text class="item-action">查看</text>
          <text class="arrow">></text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 个人资料可见性选择弹窗 -->
<view class="modal {{showVisibilityModal ? 'show' : ''}}" bindtap="hideVisibilityModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">个人资料可见性</text>
      <text class="modal-close" bindtap="hideVisibilityModal">×</text>
    </view>
    <view class="modal-body">
      <view class="option-list">
        <view 
          class="option-item {{profileVisibility === '所有人' ? 'selected' : ''}}"
          bindtap="selectVisibility"
          data-value="所有人"
        >
          <text class="option-icon">🌍</text>
          <view class="option-info">
            <text class="option-title">所有人</text>
            <text class="option-desc">任何人都可以查看您的个人资料</text>
          </view>
          <text class="option-check">{{profileVisibility === '所有人' ? '✓' : ''}}</text>
        </view>
        <view 
          class="option-item {{profileVisibility === '仅好友' ? 'selected' : ''}}"
          bindtap="selectVisibility"
          data-value="仅好友"
        >
          <text class="option-icon">👥</text>
          <view class="option-info">
            <text class="option-title">仅好友</text>
            <text class="option-desc">只有您的好友可以查看</text>
          </view>
          <text class="option-check">{{profileVisibility === '仅好友' ? '✓' : ''}}</text>
        </view>
        <view 
          class="option-item {{profileVisibility === '仅自己' ? 'selected' : ''}}"
          bindtap="selectVisibility"
          data-value="仅自己"
        >
          <text class="option-icon">🔒</text>
          <view class="option-info">
            <text class="option-title">仅自己</text>
            <text class="option-desc">只有您自己可以查看</text>
          </view>
          <text class="option-check">{{profileVisibility === '仅自己' ? '✓' : ''}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
