Page({
  data: {
    helpCategories: [
      {
        id: 1,
        title: '快速入门',
        icon: '🚀',
        items: [
          { title: '如何开始第一次检测', content: '连接设备 → 放入样品 → 开始检测 → 查看结果' },
          { title: '设备连接指南', content: '打开蓝牙 → 扫描设备 → 选择设备 → 确认连接' },
          { title: '基本操作流程', content: '登录账号 → 连接设备 → 进行检测 → 保存结果' }
        ]
      },
      {
        id: 2,
        title: '设备使用',
        icon: '📱',
        items: [
          { title: '设备连接问题', content: '确保设备已开机 → 检查蓝牙连接 → 重新扫描设备' },
          { title: '检测精度说明', content: '铝含量精度：±0.5% → 硅含量精度：±0.3% → 铁含量精度：±0.4%' },
          { title: '设备维护保养', content: '定期清洁传感器 → 避免潮湿环境 → 定期校准设备' }
        ]
      },
      {
        id: 3,
        title: '数据管理',
        icon: '📊',
        items: [
          { title: '检测记录查看', content: '历史记录页面 → 选择时间范围 → 查看详细数据' },
          { title: '数据导出方法', content: '选择导出格式 → 确认导出范围 → 下载文件' },
          { title: '数据分析功能', content: '趋势分析 → 质量评估 → 建议报告' }
        ]
      },
      {
        id: 4,
        title: '常见问题',
        icon: '❓',
        items: [
          { title: '检测结果异常怎么办', content: '检查样品质量 → 重新校准设备 → 联系技术支持' },
          { title: '设备无法连接', content: '重启设备 → 清除蓝牙缓存 → 重新配对' },
          { title: '数据同步失败', content: '检查网络连接 → 重新登录账号 → 手动同步' }
        ]
      }
    ],
    expandedCategory: null
  },

  toggleCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    const expanded = this.data.expandedCategory === categoryId ? null : categoryId
    this.setData({
      expandedCategory: expanded
    })
  },

  contactSupport() {
    wx.showActionSheet({
      itemList: ['在线客服', '电话支持', '邮件反馈'],
      success: (res) => {
        const contacts = [
          '功能开发中',
          '客服电话：400-123-4567',
          '邮箱：<EMAIL>'
        ]
        wx.showToast({
          title: contacts[res.tapIndex],
          icon: 'none',
          duration: 3000
        })
      }
    })
  },

  viewVideoTutorial() {
    wx.showToast({
      title: '视频教程功能开发中',
      icon: 'none'
    })
  }
})
