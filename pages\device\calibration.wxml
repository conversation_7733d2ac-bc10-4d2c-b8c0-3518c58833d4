<view class="container">
  <!-- 校准步骤 -->
  <view class="calibration-steps">
    <view class="step-item" wx:for="{{calibrationSteps}}" wx:key="id">
      <view class="step-indicator">
        <view class="step-number {{item.status}}">
          <text wx:if="{{item.status === 'pending'}}">{{item.id}}</text>
          <view wx:elif="{{item.status === 'active'}}" class="loading-dot"></view>
          <text wx:else>✓</text>
        </view>
        <view wx:if="{{index < calibrationSteps.length - 1}}" class="step-line {{item.status === 'completed' ? 'completed' : ''}}"></view>
      </view>
      <view class="step-content">
        <text class="step-title">{{item.title}}</text>
        <text class="step-description">{{item.description}}</text>
      </view>
    </view>
  </view>

  <!-- 校准结果 -->
  <view class="calibration-result" wx:if="{{calibrationResult}}">
    <view class="result-header">
      <view class="result-icon">✅</view>
      <text class="result-title">校准完成</text>
    </view>
    <view class="result-details">
      <view class="result-item">
        <text class="result-label">铝含量精度</text>
        <text class="result-value">{{calibrationResult.alAccuracy}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">硅含量精度</text>
        <text class="result-value">{{calibrationResult.siAccuracy}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">铁含量精度</text>
        <text class="result-value">{{calibrationResult.feAccuracy}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">校准时间</text>
        <text class="result-value">{{calibrationResult.calibrationTime}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      wx:if="{{!calibrating && !calibrationResult}}"
      class="action-btn primary" 
      bindtap="startCalibration"
    >
      开始校准
    </button>
    
    <button 
      wx:if="{{calibrating}}"
      class="action-btn disabled" 
      disabled
    >
      校准中...
    </button>
    
    <view wx:if="{{calibrationResult}}" class="result-buttons">
      <button class="action-btn secondary" bindtap="resetCalibration">重新校准</button>
      <button class="action-btn tertiary" bindtap="viewCalibrationHistory">校准历史</button>
    </view>
  </view>
</view>
