/* pages/privacy/privacy.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 导航栏 */
.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.header-left {
  display: flex;
  align-items: center;
  color: #666;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 32rpx;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-right: 80rpx;
}

/* 隐私概览 */
.privacy-overview {
  display: flex;
  align-items: center;
  background-color: #fff;
  margin: 20rpx 30rpx;
  padding: 40rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.overview-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
}

.overview-content {
  flex: 1;
}

.overview-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.overview-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 设置区域 */
.section {
  margin: 20rpx 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding: 0 10rpx;
}

.setting-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.item-right {
  display: flex;
  align-items: center;
}

.item-value {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.item-action {
  font-size: 28rpx;
  color: #2B85E4;
  margin-right: 10rpx;
}

.item-action.danger {
  color: #ff4757;
}

.arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  z-index: 1000;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

/* 选项列表 */
.option-list {
  display: flex;
  flex-direction: column;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item.selected {
  background-color: #f0f8ff;
}

.option-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.option-info {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.option-check {
  font-size: 32rpx;
  color: #2B85E4;
  font-weight: bold;
  width: 40rpx;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .privacy-overview {
    flex-direction: column;
    text-align: center;
  }
  
  .overview-icon {
    margin-right: 0;
    margin-bottom: 20rpx;
  }
  
  .item-left {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .item-icon {
    margin-bottom: 10rpx;
  }
}

/* 动画效果 */
.section {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 开关样式优化 */
switch {
  transform: scale(0.8);
}

/* 危险操作样式 */
.setting-item:has(.danger) {
  background-color: #fff5f5;
}

.setting-item:has(.danger):active {
  background-color: #ffe5e5;
}
