const app = getApp()
const db = wx.cloud.database()

Page({
  data: {
    historyList: [],
    loading: true,
    currentTab: 0,
    tabs: ['全部', '今日', '本周', '本月'],
    filterType: 'all'
  },

  onLoad() {
    this.loadHistory()
  },

  onShow() {
    this.loadHistory()
  },

  // 加载检测历史
  loadHistory() {
    this.setData({ loading: true })
    
    const openid = app.globalData.openid || wx.getStorageSync('openid')
    if (!openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      })
      this.setData({ loading: false })
      return
    }

    let query = db.collection('detections').where({
      _openid: openid
    })

    // 根据筛选类型添加时间条件
    const now = new Date()
    switch (this.data.filterType) {
      case 'today':
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        query = query.where({
          createTime: db.command.gte(today)
        })
        break
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        query = query.where({
          createTime: db.command.gte(weekAgo)
        })
        break
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        query = query.where({
          createTime: db.command.gte(monthAgo)
        })
        break
    }

    query.orderBy('createTime', 'desc').get()
      .then(res => {
        const historyList = res.data.map(item => ({
          ...item,
          date: this.formatDate(item.createTime),
          time: this.formatTime(item.createTime)
        }))
        
        this.setData({
          historyList,
          loading: false
        })
      })
      .catch(err => {
        console.error('加载历史记录失败', err)
        wx.showToast({
          title: '加载失败',
          icon: 'error'
        })
        this.setData({ loading: false })
      })
  },

  // 切换标签页
  switchTab(e) {
    const index = e.currentTarget.dataset.index
    const filterTypes = ['all', 'today', 'week', 'month']
    
    this.setData({
      currentTab: index,
      filterType: filterTypes[index]
    })
    
    this.loadHistory()
  },

  // 查看详情
  viewDetail(e) {
    const item = e.currentTarget.dataset.item
    wx.navigateTo({
      url: `/pages/detection/detail?id=${item._id}`
    })
  },

  // 删除记录
  deleteRecord(e) {
    const item = e.currentTarget.dataset.item
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条检测记录吗？',
      success: (res) => {
        if (res.confirm) {
          db.collection('detections').doc(item._id).remove()
            .then(() => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.loadHistory()
            })
            .catch(err => {
              console.error('删除失败', err)
              wx.showToast({
                title: '删除失败',
                icon: 'error'
              })
            })
        }
      }
    })
  },

  // 导出数据
  exportData() {
    if (this.data.historyList.length === 0) {
      wx.showToast({
        title: '暂无数据',
        icon: 'none'
      })
      return
    }

    wx.showActionSheet({
      itemList: ['导出为Excel', '导出为PDF', '分享数据'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.exportToExcel()
            break
          case 1:
            this.exportToPDF()
            break
          case 2:
            this.shareData()
            break
        }
      }
    })
  },

  // 导出为Excel
  exportToExcel() {
    wx.showLoading({ title: '导出中...' })
    
    wx.cloud.callFunction({
      name: 'exportData',
      data: {
        type: 'excel',
        data: this.data.historyList
      },
      success: (res) => {
        if (res.result.success) {
          wx.downloadFile({
            url: res.result.fileUrl,
            success: (downloadRes) => {
              wx.saveFile({
                tempFilePath: downloadRes.tempFilePath,
                success: () => {
                  wx.showToast({
                    title: '导出成功',
                    icon: 'success'
                  })
                }
              })
            }
          })
        }
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  },

  // 格式化日期
  formatDate(timestamp) {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${hours}:${minutes}`
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadHistory()
    wx.stopPullDownRefresh()
  }
})
