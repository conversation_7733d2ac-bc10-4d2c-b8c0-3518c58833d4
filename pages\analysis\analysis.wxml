<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon">📊</view>
    <text class="loading-text">正在分析数据...</text>
  </view>

  <!-- 分析内容 -->
  <view class="analysis-content" wx:else>
    <!-- 时间范围选择 -->
    <view class="time-range-selector">
      <view class="range-item {{timeRange === 'week' ? 'active' : ''}}" 
            bindtap="switchTimeRange" data-range="week">
        <text>近一周</text>
      </view>
      <view class="range-item {{timeRange === 'month' ? 'active' : ''}}" 
            bindtap="switchTimeRange" data-range="month">
        <text>近一月</text>
      </view>
      <view class="range-item {{timeRange === 'quarter' ? 'active' : ''}}" 
            bindtap="switchTimeRange" data-range="quarter">
        <text>近三月</text>
      </view>
      <view class="range-item {{timeRange === 'year' ? 'active' : ''}}" 
            bindtap="switchTimeRange" data-range="year">
        <text>近一年</text>
      </view>
    </view>

    <!-- 概览统计 -->
    <view class="overview-stats">
      <view class="stat-card">
        <view class="stat-icon">🔬</view>
        <view class="stat-info">
          <text class="stat-number">{{analysisData.totalSamples}}</text>
          <text class="stat-label">检测样品</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon">⚡</view>
        <view class="stat-info">
          <text class="stat-number">{{analysisData.avgAlContent}}%</text>
          <text class="stat-label">平均铝含量</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-icon">📈</view>
        <view class="stat-info">
          <text class="stat-number">{{analysisData.qualityDistribution.excellent + analysisData.qualityDistribution.good}}</text>
          <text class="stat-label">优质样品</text>
        </view>
      </view>
    </view>

    <!-- 成分分析图表 -->
    <view class="chart-section">
      <view class="section-header">
        <view class="section-icon">📊</view>
        <text class="section-title">平均成分分析</text>
      </view>
      
      <view class="composition-chart">
        <view class="chart-item" wx:for="{{chartData.composition}}" wx:key="name">
          <view class="chart-bar">
            <view class="bar-fill" style="width: {{item.value * 2}}%; background-color: {{item.color}};"></view>
          </view>
          <view class="chart-label">
            <text class="element-name">{{item.name}}</text>
            <text class="element-value">{{item.value}}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 质量分布 -->
    <view class="chart-section">
      <view class="section-header">
        <view class="section-icon">🎯</view>
        <text class="section-title">质量分布</text>
      </view>
      
      <view class="quality-distribution">
        <view class="quality-item" wx:for="{{chartData.quality}}" wx:key="name">
          <view class="quality-circle" style="background-color: {{item.color}};">
            <text class="quality-count">{{item.value}}</text>
          </view>
          <text class="quality-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 趋势分析 -->
    <view class="chart-section" wx:if="{{chartData.trend.length > 0}}">
      <view class="section-header">
        <view class="section-icon">📈</view>
        <text class="section-title">铝含量趋势</text>
      </view>
      
      <view class="trend-chart">
        <view class="trend-item" wx:for="{{chartData.trend}}" wx:key="date">
          <view class="trend-bar">
            <view class="trend-fill" style="height: {{item.avgAlContent * 2}}%;"></view>
          </view>
          <text class="trend-date">{{item.date}}</text>
        </view>
      </view>
    </view>

    <!-- 详细数据 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-icon">📋</view>
        <text class="section-title">详细数据</text>
      </view>
      
      <view class="detail-grid">
        <view class="detail-item">
          <text class="detail-label">平均硅含量</text>
          <text class="detail-value">{{analysisData.avgSiContent}}%</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">平均铁含量</text>
          <text class="detail-value">{{analysisData.avgFeContent}}%</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">平均钛含量</text>
          <text class="detail-value">{{analysisData.avgTiContent}}%</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">平均水分</text>
          <text class="detail-value">{{analysisData.avgMoisture}}%</text>
        </view>
      </view>
    </view>

    <!-- 热门地点 -->
    <view class="location-section" wx:if="{{analysisData.topLocations.length > 0}}">
      <view class="section-header">
        <view class="section-icon">📍</view>
        <text class="section-title">检测地点统计</text>
      </view>
      
      <view class="location-list">
        <view class="location-item" wx:for="{{analysisData.topLocations}}" wx:key="location">
          <text class="location-name">{{item.location}}</text>
          <text class="location-count">{{item.count}}次</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="exportReport">
        <view class="btn-icon">📄</view>
        <text>导出报告</text>
      </button>
      
      <button class="action-btn secondary" bindtap="viewDetailData">
        <view class="btn-icon">📊</view>
        <text>查看详情</text>
      </button>
      
      <button class="action-btn secondary" bindtap="shareAnalysis">
        <view class="btn-icon">📤</view>
        <text>分享结果</text>
      </button>
    </view>
  </view>

  <!-- 导出对话框 -->
  <view class="export-dialog" wx:if="{{showExportDialog}}">
    <view class="dialog-mask" bindtap="cancelExport"></view>
    <view class="dialog-content">
      <view class="dialog-header">
        <text class="dialog-title">选择导出格式</text>
      </view>
      
      <view class="format-options">
        <view class="format-item" bindtap="confirmExport" data-format="pdf">
          <view class="format-icon">📄</view>
          <text class="format-name">PDF报告</text>
          <text class="format-desc">适合打印和存档</text>
        </view>
        
        <view class="format-item" bindtap="confirmExport" data-format="excel">
          <view class="format-icon">📊</view>
          <text class="format-name">Excel表格</text>
          <text class="format-desc">便于数据处理</text>
        </view>
        
        <view class="format-item" bindtap="confirmExport" data-format="image">
          <view class="format-icon">🖼️</view>
          <text class="format-name">图片格式</text>
          <text class="format-desc">方便分享</text>
        </view>
      </view>
      
      <view class="dialog-actions">
        <button class="dialog-btn cancel" bindtap="cancelExport">取消</button>
      </view>
    </view>
  </view>
</view>
